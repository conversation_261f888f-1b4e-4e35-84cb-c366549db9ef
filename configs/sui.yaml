# Sui链完整配置文件

# 应用程序配置
app:
  name: "unified-tx-parser"
  version: "1.0.0"
  port: 8081

# Redis配置 - 用于进度跟踪和缓存
redis:
  host: "localhost"  # Docker环境中使用 "redis"
  port: 6379
  password: ""
  db: 0
  maxRetries: 3
  poolSize: 10
  dialTimeout: 5     # 连接超时(秒)
  readTimeout: 3     # 读取超时(秒)
  writeTimeout: 3    # 写入超时(秒)
  minIdleConns: 2    # 最小空闲连接数

# 链配置
chains:
  sui:
    enabled: true
    rpc_endpoint: "https://fullnode.mainnet.sui.io:443"
    chain_id: "sui-mainnet"
    batch_size: 10
    timeout: 30 # 秒
    retry_count: 3

# 协议配置 - Sui链上的协议
protocols:
  bluefin:
    enabled: true
    chain: "sui"
    contract_addresses:
      - "0x3492c874c1e3b3e2984e8c41b589e642d4d0a5d6459e5a9cfc2d52fd7c89c267"

# 处理器配置
processor:
  batch_size: 10
  max_concurrent: 10
  retry_delay: 5 # 秒
  max_retries: 3

# 日志配置
logging:
  level: "info"
  format: "text"
  output: "stdout"

# 存储配置 - 支持多种存储引擎
storage:
  # 存储引擎类型: mysql, redis, memory, influxdb
  type: "influxdb"

  # MySQL配置
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: "password123"
    database: "unified_tx_parser"
    charset: "utf8mb4"
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 3600  # 秒

  # InfluxDB配置
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "sui"  # Sui链专用bucket
    batch_size: 1000
    flush_time: 10  # 秒
    precision: "ms"  # 时间精度: ns, us, ms, s
