# Solana链完整配置文件

# 应用程序配置
app:
  name: "unified-tx-parser"
  version: "1.0.0"
  port: 8081

# Redis配置 - 用于进度跟踪和缓存
redis:
  host: "localhost"  # Docker环境中使用 "redis"
  port: 6379
  password: ""
  db: 0
  maxRetries: 3
  poolSize: 10
  dialTimeout: 5     # 连接超时(秒)
  readTimeout: 3     # 读取超时(秒)
  writeTimeout: 3    # 写入超时(秒)
  minIdleConns: 2    # 最小空闲连接数

# 链配置
chains:
  solana:
    enabled: true
    rpc_endpoint: "https://api.mainnet-beta.solana.com"
    chain_id: "solana-mainnet"
    batch_size: 10
    timeout: 30
    retry_count: 3

# 协议配置 - Solana链上的协议
protocols:
  jupiter:
    enabled: true
    chain: "solana"
    contract_addresses: []

# 处理器配置 - Solana通常有较好的性能，可以使用默认配置
processor:
  batch_size: 10
  max_concurrent: 10
  retry_delay: 5
  max_retries: 3

# 日志配置
logging:
  level: "info"
  format: "text"
  output: "stdout"

# 存储配置 - 支持多种存储引擎
storage:
  # 存储引擎类型: mysql, redis, memory, influxdb
  type: "influxdb"

  # MySQL配置
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: "123456"
    database: "unified_tx_parser"
    charset: "utf8mb4"
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 3600  # 秒

  # InfluxDB配置
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "solana"  # Solana链专用bucket
    batch_size: 1000
    flush_time: 10  # 秒
    precision: "ms"  # 时间精度: ns, us, ms, s
