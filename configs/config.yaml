# 统一交易处理器配置
app:
  name: "unified-tx-parser"
  version: "1.0.0"
  port: 8081

# Redis配置
redis:
  host: "localhost"  # Docker环境中使用 "redis"
  port: 6379
  password: ""
  db: 0
  maxRetries: 3
  poolSize: 10
  dialTimeout: 5     # 连接超时(秒)
  readTimeout: 3     # 读取超时(秒)
  writeTimeout: 3    # 写入超时(秒)
  minIdleConns: 2    # 最小空闲连接数

# 链配置
chains:
  sui:
    enabled: true
    rpc_endpoint: "https://fullnode.mainnet.sui.io:443"
    chain_id: "sui-mainnet"
    batch_size: 10
    timeout: 30 # 秒
    retry_count: 3

  ethereum:
    enabled: false
    rpc_endpoint: "https://eth.rpc.blxrbdn.com"  # 使用响应最快的端点 (469ms)
    chain_id: "ethereum-mainnet"
    batch_size: 10      # 以太坊单个区块处理，避免超时
    timeout: 120       # 增加超时时间到2分钟
    retry_count: 3     # 适中的重试次数
    start_block: 0

  bsc:
    enabled: false
    rpc_endpoint: "https://bsc.publicnode.com"  # 使用测试通过的端点
    chain_id: "bsc-mainnet"
    batch_size: 10      # BSC使用更小批次，避免超时
    timeout: 90        # 增加超时时间
    retry_count: 3
    start_block: 0

  solana:
    enabled: false
    rpc_endpoint: "https://api.mainnet-beta.solana.com"
    chain_id: "solana-mainnet"
    batch_size: 10
    timeout: 30
    retry_count: 3

# 协议配置
protocols:
  bluefin:
    enabled: true
    chain: "sui"
    contract_addresses:
      - "0x3492c874c1e3b3e2984e8c41b589e642d4d0a5d6459e5a9cfc2d52fd7c89c267"
  
  uniswap:
    enabled: false
    chain: "ethereum"
    contract_addresses: []
  
  pancakeswap:
    enabled: false
    chain: "bsc"
    contract_addresses: []

  jupiter:
    enabled: false
    chain: "solana"
    contract_addresses: []

# 处理器配置
processor:
  batch_size: 10
  max_concurrent: 10
  retry_delay: 5 # 秒
  max_retries: 3

# 日志配置
logging:
  level: "info"
  format: "text"
  output: "stdout"

# 存储配置
storage:
  # 存储引擎类型: mysql, redis, memory, influxdb
  type: "influxdb"

  # MySQL配置
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: "123456"
    database: "unified_tx_parser"
    charset: "utf8mb4"
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 3600  # 秒

  # InfluxDB配置
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "blockchain-data"  # 传统单体模式使用的通用bucket
    batch_size: 1000
    flush_time: 10  # 秒
    precision: "ms"  # 时间精度: ns, us, ms, s