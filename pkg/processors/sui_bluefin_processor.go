package processors

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"time"

	"unified-tx-parser/pkg/core"
)

// SuiBlueFinProcessor Sui-BlueFin统一处理器
type SuiBlueFinProcessor struct {
	*core.BaseChainDEXProcessor

	// Sui特定客户端
	suiClient SuiClient

	// BlueFin特定解析器
	blueFinParser BlueFinParser
}

// SuiClient Sui区块链客户端接口
type SuiClient interface {
	GetLatestCheckpoint(ctx context.Context) (*big.Int, error)
	GetTransactionsByCheckpointRange(ctx context.Context, start, end *big.Int) ([]core.UnifiedTransaction, error)
	GetTransaction(ctx context.Context, digest string) (*core.UnifiedTransaction, error)
	HealthCheck(ctx context.Context) error
}

// BlueFinParser BlueFin协议解析器接口
type BlueFinParser interface {
	SupportsTransaction(tx *core.UnifiedTransaction) bool
	ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error)
	GetContractAddresses() []string
	GetEventSignatures() []string
}

// NewSuiBlueFinProcessor 创建Sui-BlueFin处理器
func NewSuiBlueFinProcessor(config *core.ChainDEXConfig) (*SuiBlueFinProcessor, error) {
	base := core.NewBaseChainDEXProcessor(config)

	processor := &SuiBlueFinProcessor{
		BaseChainDEXProcessor: base,
	}

	// 初始化Sui客户端
	suiClient, err := NewSuiClient(config.RPCEndpoint, config.Timeout)
	if err != nil {
		return nil, fmt.Errorf("初始化Sui客户端失败: %v", err)
	}
	processor.suiClient = suiClient

	// 初始化BlueFin解析器
	blueFinConfig := extractBlueFinConfig(config)
	blueFinParser, err := NewBlueFinParser(blueFinConfig)
	if err != nil {
		return nil, fmt.Errorf("初始化BlueFin解析器失败: %v", err)
	}
	processor.blueFinParser = blueFinParser

	return processor, nil
}

// GetLatestBlockNumber 获取最新区块号（Sui中是checkpoint）
func (p *SuiBlueFinProcessor) GetLatestBlockNumber(ctx context.Context) (*big.Int, error) {
	return p.suiClient.GetLatestCheckpoint(ctx)
}

// GetTransactionsByBlockRange 获取区块范围内的交易
func (p *SuiBlueFinProcessor) GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]core.UnifiedTransaction, error) {
	return p.suiClient.GetTransactionsByCheckpointRange(ctx, startBlock, endBlock)
}

// ExtractDEXEvents 提取DEX事件
func (p *SuiBlueFinProcessor) ExtractDEXEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	if !p.blueFinParser.SupportsTransaction(tx) {
		return nil, nil
	}

	return p.blueFinParser.ExtractEvents(ctx, tx)
}

// SupportsTransaction 检查是否支持该交易
func (p *SuiBlueFinProcessor) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	return p.blueFinParser.SupportsTransaction(tx)
}

// ProcessBlockRange 处理区块范围 - 核心处理方法
func (p *SuiBlueFinProcessor) ProcessBlockRange(ctx context.Context, startBlock, endBlock *big.Int) (*core.ProcessingResult, error) {
	result := &core.ProcessingResult{
		StartTime:  time.Now(),
		StartBlock: startBlock,
		EndBlock:   endBlock,
		Errors:     make([]error, 0),
	}

	log.Printf("🔄 [%s] 开始处理区块范围: %s - %s", p.GetProcessorID(), startBlock.String(), endBlock.String())

	// 1. 获取交易数据
	transactions, err := p.GetTransactionsByBlockRange(ctx, startBlock, endBlock)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Errorf("获取交易失败: %v", err))
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		return result, err
	}

	result.ProcessedTransactions = int64(len(transactions))
	log.Printf("📊 [%s] 获取到 %d 笔交易", p.GetProcessorID(), len(transactions))

	// 2. 过滤和解析DEX事件
	var filteredTransactions []core.UnifiedTransaction
	var allEvents []core.BusinessEvent

	for _, tx := range transactions {
		// 检查是否包含DEX事件
		events, err := p.ExtractDEXEvents(ctx, &tx)
		if err != nil {
			log.Printf("⚠️ [%s] 解析交易 %s 失败: %v", p.GetProcessorID(), tx.Hash, err)
			result.Errors = append(result.Errors, err)
			continue
		}

		// 只保留包含DEX事件的交易
		if len(events) > 0 {
			filteredTransactions = append(filteredTransactions, tx)
			allEvents = append(allEvents, events...)
		}
	}

	result.FilteredTransactions = int64(len(filteredTransactions))
	result.ExtractedEvents = int64(len(allEvents))
	result.ProcessedBlocks = endBlock.Int64() - startBlock.Int64() + 1

	// 3. 设置结果数据
	result.Transactions = filteredTransactions
	result.Events = allEvents

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	log.Printf("✅ [%s] 处理完成: 区块 %d 个, 交易 %d/%d 个, 事件 %d 个, 耗时 %v",
		p.GetProcessorID(),
		result.ProcessedBlocks,
		result.FilteredTransactions,
		result.ProcessedTransactions,
		result.ExtractedEvents,
		result.Duration)

	return result, nil
}

// HealthCheck 健康检查
func (p *SuiBlueFinProcessor) HealthCheck(ctx context.Context) error {
	// 检查Sui客户端
	if err := p.suiClient.HealthCheck(ctx); err != nil {
		return fmt.Errorf("Sui客户端健康检查失败: %v", err)
	}

	// 检查BlueFin解析器配置
	if len(p.blueFinParser.GetContractAddresses()) == 0 {
		return fmt.Errorf("BlueFin合约地址未配置")
	}

	return nil
}

// SuiBlueFinProcessorFactory Sui-BlueFin处理器工厂
type SuiBlueFinProcessorFactory struct{}

// CreateProcessor 创建处理器
func (f *SuiBlueFinProcessorFactory) CreateProcessor(config *core.ChainDEXConfig) (core.ChainDEXProcessor, error) {
	// 验证配置
	if err := f.ValidateConfig(config); err != nil {
		return nil, err
	}

	return NewSuiBlueFinProcessor(config)
}

// GetSupportedCombinations 获取支持的组合
func (f *SuiBlueFinProcessorFactory) GetSupportedCombinations() []string {
	return []string{"sui-bluefin", "sui"}
}

// ValidateConfig 验证配置
func (f *SuiBlueFinProcessorFactory) ValidateConfig(config *core.ChainDEXConfig) error {
	if config.ChainType != core.ChainTypeSui {
		return fmt.Errorf("不支持的链类型: %s", config.ChainType)
	}

	if config.RPCEndpoint == "" {
		return fmt.Errorf("RPC端点不能为空")
	}

	// 检查是否有BlueFin协议配置
	hasBlueFinConfig := false
	for _, protocol := range config.DEXProtocols {
		if protocol.Name == "bluefin" && protocol.Enabled {
			hasBlueFinConfig = true
			if len(protocol.ContractAddresses) == 0 {
				return fmt.Errorf("BlueFin协议缺少合约地址配置")
			}
			break
		}
	}

	if !hasBlueFinConfig {
		return fmt.Errorf("缺少BlueFin协议配置")
	}

	return nil
}

// 辅助函数

// extractBlueFinConfig 从配置中提取BlueFin相关配置
func extractBlueFinConfig(config *core.ChainDEXConfig) map[string]interface{} {
	for _, protocol := range config.DEXProtocols {
		if protocol.Name == "bluefin" && protocol.Enabled {
			result := make(map[string]interface{})
			result["contract_addresses"] = protocol.ContractAddresses
			result["event_signatures"] = protocol.EventSignatures
			for k, v := range protocol.Config {
				result[k] = v
			}
			return result
		}
	}
	return make(map[string]interface{})
}

// NewSuiClient 创建Sui客户端
func NewSuiClient(endpoint string, timeout int) (SuiClient, error) {
	return NewRealSuiClient(endpoint, timeout)
}

// NewBlueFinParser 创建BlueFin解析器
func NewBlueFinParser(config map[string]interface{}) (BlueFinParser, error) {
	return NewRealBlueFinParser(config)
}
