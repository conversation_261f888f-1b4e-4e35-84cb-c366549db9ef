package dex

import (
	"context"
	"log"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"
)

// DEXExtractor 通用DEX事件提取器
type DEXExtractor struct {
	// 支持的协议处理器
	protocolHandlers map[string]ProtocolHandler

	// 支持的链类型
	supportedChains []core.ChainType
}

// ProtocolHandler 协议处理器接口
type ProtocolHandler interface {
	// 获取协议名称
	GetProtocolName() string

	// 检查是否支持该交易
	SupportsTransaction(tx *core.UnifiedTransaction) bool

	// 提取事件
	ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error)

	// 获取支持的链类型
	GetSupportedChains() []core.ChainType
}

// NewDEXExtractor 创建DEX提取器
func NewDEXExtractor() *DEXExtractor {
	extractor := &DEXExtractor{
		protocolHandlers: make(map[string]ProtocolHandler),
		supportedChains: []core.ChainType{
			core.ChainTypeEthereum,
			core.ChainTypeSolana,
			core.ChainTypeSui,
		},
	}

	// 注册内置协议处理器
	extractor.registerBuiltinHandlers()

	return extractor
}

// NewDEXExtractorWithConfig 根据配置创建DEX事件提取器
func NewDEXExtractorWithConfig(protocols map[string]interface{}) *DEXExtractor {
	extractor := &DEXExtractor{
		protocolHandlers: make(map[string]ProtocolHandler),
		supportedChains:  make([]core.ChainType, 0),
	}

	// 根据配置选择性注册协议处理器
	extractor.registerConfiguredHandlers(protocols)

	return extractor
}

// registerBuiltinHandlers 注册内置协议处理器
func (d *DEXExtractor) registerBuiltinHandlers() {
	// 注册Uniswap处理器 (以太坊/BSC)
	uniswapHandler := NewUniswapHandler()
	d.registerProtocolHandlerSilent(uniswapHandler)

	// 注册Bluefin处理器 (Sui)
	bluefinHandler := NewBluefinHandler()
	d.registerProtocolHandlerSilent(bluefinHandler)

	// 注册Jupiter处理器 (Solana)
	jupiterHandler := NewJupiterHandler()
	d.registerProtocolHandlerSilent(jupiterHandler)

	// 注册PancakeSwap处理器 (BSC)
	pancakeHandler := NewPancakeSwapHandler()
	d.registerProtocolHandlerSilent(pancakeHandler)

	// 统一输出注册结果，显示链-DEX映射
	chainDexMap := make(map[string][]string)
	for name, handler := range d.protocolHandlers {
		chains := handler.GetSupportedChains()
		for _, chain := range chains {
			chainStr := strings.ToUpper(string(chain))
			chainDexMap[chainStr] = append(chainDexMap[chainStr], name)
		}
	}

	log.Printf("📦 协议映射:")
	for chain, dexs := range chainDexMap {
		log.Printf("   [%s] %v", chain, dexs)
	}
}

// registerConfiguredHandlers 根据配置注册协议处理器
func (d *DEXExtractor) registerConfiguredHandlers(protocols map[string]interface{}) {
	registeredProtocols := make([]string, 0)

	// 检查每个协议是否在配置中启用
	for protocolName := range protocols {
		var handler ProtocolHandler

		switch strings.ToLower(protocolName) {
		case "uniswap":
			handler = NewUniswapHandler()
		case "bluefin":
			handler = NewBluefinHandler()
		case "jupiter":
			handler = NewJupiterHandler()
		case "pancakeswap":
			handler = NewPancakeSwapHandler()
		default:
			continue // 跳过未知协议
		}

		if handler != nil {
			d.registerProtocolHandlerSilent(handler)
			registeredProtocols = append(registeredProtocols, protocolName)
		}
	}

	// 只输出已注册的协议
	if len(registeredProtocols) > 0 {
		log.Printf("📦 已注册协议: %v", registeredProtocols)
	}
}

// RegisterProtocolHandler 注册协议处理器（带日志）
func (d *DEXExtractor) RegisterProtocolHandler(handler ProtocolHandler) {
	d.registerProtocolHandlerSilent(handler)
	protocolName := handler.GetProtocolName()
	handlerChains := handler.GetSupportedChains()
	log.Printf("📦 注册DEX协议处理器: %s (支持链: %v)", protocolName, handlerChains)
}

// registerProtocolHandlerSilent 静默注册协议处理器（无日志）
func (d *DEXExtractor) registerProtocolHandlerSilent(handler ProtocolHandler) {
	protocolName := handler.GetProtocolName()
	d.protocolHandlers[protocolName] = handler

	// 更新支持的链类型
	handlerChains := handler.GetSupportedChains()
	for _, chain := range handlerChains {
		// 检查是否已存在
		found := false
		for _, existingChain := range d.supportedChains {
			if existingChain == chain {
				found = true
				break
			}
		}
		if !found {
			d.supportedChains = append(d.supportedChains, chain)
		}
	}
}

// GetSupportedProtocols 获取支持的协议
func (d *DEXExtractor) GetSupportedProtocols() []string {
	protocols := make([]string, 0, len(d.protocolHandlers))
	for protocol := range d.protocolHandlers {
		protocols = append(protocols, protocol)
	}
	return protocols
}

// GetSupportedChains 获取支持的链类型
func (d *DEXExtractor) GetSupportedChains() []core.ChainType {
	return d.supportedChains
}

// ExtractEvents 从交易中提取业务事件
func (d *DEXExtractor) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	var allEvents []core.BusinessEvent

	// 遍历所有协议处理器
	for protocolName, handler := range d.protocolHandlers {
		// 检查是否支持该交易
		if !handler.SupportsTransaction(tx) {
			continue
		}

		// 提取事件
		events, err := handler.ExtractEvents(ctx, tx)
		if err != nil {
			log.Printf("⚠️ %s-%s: 提取失败 - %v", tx.ChainType, protocolName, err)
			continue
		}

		if len(events) > 0 {
			// 简化日志：只显示关键信息，不每个事件都打印
			allEvents = append(allEvents, events...)
		}
	}

	return allEvents, nil
}

// SupportsTransaction 检查是否支持该交易
func (d *DEXExtractor) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	// 检查链类型是否支持
	chainSupported := false
	for _, chain := range d.supportedChains {
		if chain == tx.ChainType {
			chainSupported = true
			break
		}
	}

	if !chainSupported {
		return false
	}

	// 检查是否有任何协议处理器支持该交易
	for _, handler := range d.protocolHandlers {
		if handler.SupportsTransaction(tx) {
			return true
		}
	}

	return false
}

// BaseProtocolHandler 基础协议处理器
type BaseProtocolHandler struct {
	protocolName    string
	supportedChains []core.ChainType
}

// GetProtocolName 获取协议名称
func (b *BaseProtocolHandler) GetProtocolName() string {
	return b.protocolName
}

// GetSupportedChains 获取支持的链类型
func (b *BaseProtocolHandler) GetSupportedChains() []core.ChainType {
	return b.supportedChains
}

// generateEventID 生成事件ID
func (b *BaseProtocolHandler) generateEventID(tx *core.UnifiedTransaction, eventIndex int) string {
	return utils.GenerateEventID(string(tx.ChainType), tx.TxHash, eventIndex)
}

// createBusinessEvent 创建业务事件
func (b *BaseProtocolHandler) createBusinessEvent(tx *core.UnifiedTransaction, eventType core.BusinessEventType, data interface{}, eventIndex int) core.BusinessEvent {
	return core.BusinessEvent{
		EventID:   b.generateEventID(tx, eventIndex),
		EventType: eventType,
		Protocol:  b.protocolName,
		TxHash:    tx.TxHash,
		ChainType: tx.ChainType,
		Data:      data,
		Timestamp: tx.Timestamp,
	}
}

// isContractAddress 检查是否是合约地址
func (b *BaseProtocolHandler) isContractAddress(address string, knownAddresses []string) bool {
	normalizedAddr := utils.NormalizeAddress(address)

	for _, knownAddr := range knownAddresses {
		if strings.EqualFold(normalizedAddr, utils.NormalizeAddress(knownAddr)) {
			return true
		}
	}

	return false
}
