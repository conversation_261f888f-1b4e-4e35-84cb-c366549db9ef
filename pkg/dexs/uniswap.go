package dex

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// UniswapHandler Uniswap协议处理器
type UniswapHandler struct {
	BaseProtocolHandler
	v2PairABI      abi.ABI
	v3PoolABI      abi.ABI
	routerV2ABI    abi.ABI
	routerV3ABI    abi.ABI
	contractCaller *UniswapContractCaller
}

// Uniswap合约地址
var (
	// Uniswap V2
	UniswapV2Router  = common.HexToAddress("******************************************")
	UniswapV2Factory = common.HexToAddress("******************************************")

	// Uniswap V3
	UniswapV3Router  = common.HexToAddress("******************************************")
	UniswapV3Router2 = common.HexToAddress("******************************************")
	UniswapV3Factory = common.HexToAddress("******************************************")

	// 事件签名
	SwapV2EventSignature      = common.HexToHash("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822")
	SwapV3EventSignature      = common.HexToHash("0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67")
	MintV2EventSignature      = common.HexToHash("0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f")
	BurnV2EventSignature      = common.HexToHash("0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496")
	MintV3EventSignature      = common.HexToHash("0x7a53080ba414158be7ec69b987b5fb7d07dee101fe85488f0853ae16239d0bde")
	BurnV3EventSignature      = common.HexToHash("0x0c396cd989a39f4459b5fa1aed6a9a8dcdbc45908acfd67e028cd568da98982c")
	PairCreatedEventSignature = common.HexToHash("0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9")
	PoolCreatedEventSignature = common.HexToHash("0x783cca1c0412dd0d695e784568c96da2e9c22ff989357a2e8b1d9b2b4e6b7118")
)

// NewUniswapHandler 创建Uniswap处理器
func NewUniswapHandler() *UniswapHandler {
	handler := &UniswapHandler{
		BaseProtocolHandler: BaseProtocolHandler{
			protocolName: "Uniswap",
			supportedChains: []core.ChainType{
				core.ChainTypeEthereum,
			},
		},
	}

	// 初始化ABI
	if err := handler.initABIs(); err != nil {
		log.Printf("⚠️ Uniswap ABI初始化失败: %v", err)
	}

	return handler
}

// SetContractCaller 设置合约调用器
func (u *UniswapHandler) SetContractCaller(caller *UniswapContractCaller) {
	u.contractCaller = caller
}

// initABIs 初始化ABI
func (u *UniswapHandler) initABIs() error {
	// 这里应该加载真实的ABI，为了简化，我们使用基本的事件定义
	// 在实际项目中，应该从ABI文件或合约中获取

	// V2 Pair ABI (简化版)
	v2PairABIJSON := `[
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0In", "type": "uint256"},
				{"indexed": false, "name": "amount1In", "type": "uint256"},
				{"indexed": false, "name": "amount0Out", "type": "uint256"},
				{"indexed": false, "name": "amount1Out", "type": "uint256"},
				{"indexed": true, "name": "to", "type": "address"}
			],
			"name": "Swap",
			"type": "event"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0", "type": "uint256"},
				{"indexed": false, "name": "amount1", "type": "uint256"}
			],
			"name": "Mint",
			"type": "event"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0", "type": "uint256"},
				{"indexed": false, "name": "amount1", "type": "uint256"},
				{"indexed": true, "name": "to", "type": "address"}
			],
			"name": "Burn",
			"type": "event"
		}
	]`

	var err error
	u.v2PairABI, err = abi.JSON(strings.NewReader(v2PairABIJSON))
	if err != nil {
		return fmt.Errorf("解析V2 Pair ABI失败: %w", err)
	}

	return nil
}

// SupportsTransaction 检查是否支持该交易
func (u *UniswapHandler) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	// 检查链类型
	if tx.ChainType != core.ChainTypeEthereum {
		return false
	}

	// 检查是否与Uniswap相关合约交互
	uniswapAddresses := []string{
		UniswapV2Router.Hex(),
		UniswapV3Router.Hex(),
		UniswapV3Router2.Hex(),
	}

	// 检查直接交互
	if u.isContractAddress(tx.ToAddress, uniswapAddresses) {
		return true
	}

	// 检查交易日志中是否包含Uniswap事件
	return u.containsUniswapEvents(tx)
}

// containsUniswapEvents 检查是否包含Uniswap事件
func (u *UniswapHandler) containsUniswapEvents(tx *core.UnifiedTransaction) bool {
	rawData, ok := tx.RawData.(map[string]interface{})
	if !ok {
		return false
	}

	receipt, ok := rawData["receipt"].(*types.Receipt)
	if !ok {
		return false
	}

	for _, log := range receipt.Logs {
		if len(log.Topics) == 0 {
			continue
		}

		// 检查是否为Uniswap事件签名
		eventSig := log.Topics[0]
		if eventSig == SwapV2EventSignature ||
			eventSig == SwapV3EventSignature ||
			eventSig == MintV2EventSignature ||
			eventSig == BurnV2EventSignature ||
			eventSig == MintV3EventSignature ||
			eventSig == BurnV3EventSignature ||
			eventSig == PairCreatedEventSignature ||
			eventSig == PoolCreatedEventSignature {
			return true
		}
	}

	return false
}

// ExtractEvents 提取事件
func (u *UniswapHandler) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	var events []core.BusinessEvent

	rawData, ok := tx.RawData.(map[string]interface{})
	if !ok {
		return events, nil
	}

	receipt, ok := rawData["receipt"].(*types.Receipt)
	if !ok {
		return events, nil
	}

	eventIndex := 0
	for _, log := range receipt.Logs {
		if len(log.Topics) == 0 {
			continue
		}

		eventSig := log.Topics[0]
		var businessEvent *core.BusinessEvent

		switch eventSig {
		case SwapV2EventSignature:
			businessEvent = u.parseV2SwapEvent(log, tx, eventIndex)
		case SwapV3EventSignature:
			businessEvent = u.parseV3SwapEvent(log, tx, eventIndex)
		case MintV2EventSignature:
			businessEvent = u.parseV2MintEvent(log, tx, eventIndex)
		case BurnV2EventSignature:
			businessEvent = u.parseV2BurnEvent(log, tx, eventIndex)
		case MintV3EventSignature:
			businessEvent = u.parseV3MintEvent(log, tx, eventIndex)
		case BurnV3EventSignature:
			businessEvent = u.parseV3BurnEvent(log, tx, eventIndex)
		case PairCreatedEventSignature:
			businessEvent = u.parsePairCreatedEvent(log, tx, eventIndex)
		case PoolCreatedEventSignature:
			businessEvent = u.parsePoolCreatedEvent(log, tx, eventIndex)
		}

		if businessEvent != nil {
			events = append(events, *businessEvent)
			eventIndex++
		}
	}

	return events, nil
}

// parseV2SwapEvent 解析V2交换事件
func (u *UniswapHandler) parseV2SwapEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 3 || len(log.Data) < 128 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(log.Topics[1].Bytes())
	to := common.BytesToAddress(log.Topics[2].Bytes())

	// 解析data参数 (amount0In, amount1In, amount0Out, amount1Out)
	amount0In := new(big.Int).SetBytes(log.Data[0:32])
	amount1In := new(big.Int).SetBytes(log.Data[32:64])
	amount0Out := new(big.Int).SetBytes(log.Data[64:96])
	amount1Out := new(big.Int).SetBytes(log.Data[96:128])

	// 确定输入和输出代币
	var tokenIn, tokenOut string
	var amountIn, amountOut *big.Int

	if amount0In.Cmp(big.NewInt(0)) > 0 {
		// Token0 -> Token1
		tokenIn = u.getToken0Address(log.Address)
		tokenOut = u.getToken1Address(log.Address)
		amountIn = amount0In
		amountOut = amount1Out
	} else {
		// Token1 -> Token0
		tokenIn = u.getToken1Address(log.Address)
		tokenOut = u.getToken0Address(log.Address)
		amountIn = amount1In
		amountOut = amount0Out
	}

	swapData := &core.SwapEventData{
		PoolID:    log.Address.Hex(),
		TokenIn:   tokenIn,
		TokenOut:  tokenOut,
		AmountIn:  amountIn,
		AmountOut: amountOut,
		Sender:    utils.NormalizeAddress(sender.Hex()),
		Recipient: utils.NormalizeAddress(to.Hex()),
		Price:     u.calculatePrice(amountIn, amountOut),
		FeePaid:   u.calculateV2Fee(amountIn), // V2固定0.3%手续费
	}

	event := u.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex)
	return &event
}

// parseV3SwapEvent 解析V3交换事件
func (u *UniswapHandler) parseV3SwapEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 3 || len(log.Data) < 160 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(log.Topics[1].Bytes())
	recipient := common.BytesToAddress(log.Topics[2].Bytes())

	// 解析data参数 (amount0, amount1, sqrtPriceX96, liquidity, tick)
	amount0 := new(big.Int).SetBytes(log.Data[0:32])
	amount1 := new(big.Int).SetBytes(log.Data[32:64])
	// sqrtPriceX96 := new(big.Int).SetBytes(log.Data[64:96])
	// liquidity := new(big.Int).SetBytes(log.Data[96:128])
	// tick := new(big.Int).SetBytes(log.Data[128:160])

	// 确定输入和输出
	var tokenIn, tokenOut string
	var amountIn, amountOut *big.Int

	if amount0.Sign() > 0 {
		// Token0 -> Token1
		tokenIn = u.getToken0Address(log.Address)
		tokenOut = u.getToken1Address(log.Address)
		amountIn = amount0
		amountOut = new(big.Int).Abs(amount1)
	} else {
		// Token1 -> Token0
		tokenIn = u.getToken1Address(log.Address)
		tokenOut = u.getToken0Address(log.Address)
		amountIn = new(big.Int).Abs(amount1)
		amountOut = new(big.Int).Abs(amount0)
	}

	swapData := &core.SwapEventData{
		PoolID:    log.Address.Hex(),
		TokenIn:   tokenIn,
		TokenOut:  tokenOut,
		AmountIn:  amountIn,
		AmountOut: amountOut,
		Sender:    utils.NormalizeAddress(sender.Hex()),
		Recipient: utils.NormalizeAddress(recipient.Hex()),
		Price:     u.calculatePrice(amountIn, amountOut),
		FeePaid:   u.calculateV3Fee(log.Address, amountIn), // V3动态手续费
	}

	event := u.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex)
	return &event
}

// parseV2MintEvent 解析V2添加流动性事件
func (u *UniswapHandler) parseV2MintEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 2 || len(log.Data) < 64 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(log.Topics[1].Bytes())

	// 解析data参数 (amount0, amount1)
	amount0 := new(big.Int).SetBytes(log.Data[0:32])
	amount1 := new(big.Int).SetBytes(log.Data[32:64])

	liquidityData := &core.LiquidityEventData{
		PoolID:          log.Address.Hex(),
		TokenA:          u.getToken0Address(log.Address),
		TokenB:          u.getToken1Address(log.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityMinted: u.calculateLiquidityMinted(amount0, amount1),
		Provider:        utils.NormalizeAddress(sender.Hex()),
	}

	event := u.createBusinessEvent(tx, core.EventTypeAddLiquidity, liquidityData, eventIndex)
	return &event
}

// parseV2BurnEvent 解析V2移除流动性事件
func (u *UniswapHandler) parseV2BurnEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 3 || len(log.Data) < 64 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(log.Topics[1].Bytes())
	to := common.BytesToAddress(log.Topics[2].Bytes())

	// 解析data参数 (amount0, amount1)
	amount0 := new(big.Int).SetBytes(log.Data[0:32])
	amount1 := new(big.Int).SetBytes(log.Data[32:64])

	liquidityData := &core.LiquidityEventData{
		PoolID:          log.Address.Hex(),
		TokenA:          u.getToken0Address(log.Address),
		TokenB:          u.getToken1Address(log.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityBurned: u.calculateLiquidityBurned(amount0, amount1),
		Provider:        utils.NormalizeAddress(sender.Hex()),
	}

	// 如果to地址不同，说明是转给其他人
	if !strings.EqualFold(sender.Hex(), to.Hex()) {
		liquidityData.Provider = utils.NormalizeAddress(to.Hex())
	}

	event := u.createBusinessEvent(tx, core.EventTypeRemoveLiquidity, liquidityData, eventIndex)
	return &event
}

// parseV3MintEvent 解析V3添加流动性事件
func (u *UniswapHandler) parseV3MintEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 4 || len(log.Data) < 96 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(log.Topics[1].Bytes())
	owner := common.BytesToAddress(log.Topics[2].Bytes())
	// tickLower := new(big.Int).SetBytes(log.Topics[3].Bytes())
	// tickUpper := new(big.Int).SetBytes(log.Topics[4].Bytes())

	// 解析data参数 (amount, amount0, amount1)
	amount := new(big.Int).SetBytes(log.Data[0:32])
	amount0 := new(big.Int).SetBytes(log.Data[32:64])
	amount1 := new(big.Int).SetBytes(log.Data[64:96])

	liquidityData := &core.LiquidityEventData{
		PoolID:          log.Address.Hex(),
		TokenA:          u.getToken0Address(log.Address),
		TokenB:          u.getToken1Address(log.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityMinted: amount,
		Provider:        utils.NormalizeAddress(owner.Hex()),
	}

	// 如果sender不同，记录实际操作者
	if !strings.EqualFold(sender.Hex(), owner.Hex()) {
		liquidityData.Provider = utils.NormalizeAddress(sender.Hex())
	}

	event := u.createBusinessEvent(tx, core.EventTypeAddLiquidity, liquidityData, eventIndex)
	return &event
}

// parseV3BurnEvent 解析V3移除流动性事件
func (u *UniswapHandler) parseV3BurnEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 4 || len(log.Data) < 96 {
		return nil
	}

	// 解析indexed参数
	owner := common.BytesToAddress(log.Topics[1].Bytes())
	// tickLower := new(big.Int).SetBytes(log.Topics[2].Bytes())
	// tickUpper := new(big.Int).SetBytes(log.Topics[3].Bytes())

	// 解析data参数 (amount, amount0, amount1)
	amount := new(big.Int).SetBytes(log.Data[0:32])
	amount0 := new(big.Int).SetBytes(log.Data[32:64])
	amount1 := new(big.Int).SetBytes(log.Data[64:96])

	liquidityData := &core.LiquidityEventData{
		PoolID:          log.Address.Hex(),
		TokenA:          u.getToken0Address(log.Address),
		TokenB:          u.getToken1Address(log.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityBurned: amount,
		Provider:        utils.NormalizeAddress(owner.Hex()),
	}

	event := u.createBusinessEvent(tx, core.EventTypeRemoveLiquidity, liquidityData, eventIndex)
	return &event
}

// parsePairCreatedEvent 解析V2配对创建事件
func (u *UniswapHandler) parsePairCreatedEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 3 || len(log.Data) < 64 {
		return nil
	}

	// 解析indexed参数
	token0 := common.BytesToAddress(log.Topics[1].Bytes())
	token1 := common.BytesToAddress(log.Topics[2].Bytes())

	// 解析data参数 (pair, allPairsLength)
	pair := common.BytesToAddress(log.Data[12:32]) // 地址在后20字节
	// allPairsLength := new(big.Int).SetBytes(log.Data[32:64])

	poolData := map[string]interface{}{
		"pool_address": pair.Hex(),
		"token0":       token0.Hex(),
		"token1":       token1.Hex(),
		"version":      "v2",
		"fee_tier":     "3000", // V2固定0.3%
	}

	event := u.createBusinessEvent(tx, core.EventTypePoolCreated, poolData, eventIndex)
	return &event
}

// parsePoolCreatedEvent 解析V3池创建事件
func (u *UniswapHandler) parsePoolCreatedEvent(log *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(log.Topics) < 4 || len(log.Data) < 64 {
		return nil
	}

	// 解析indexed参数
	token0 := common.BytesToAddress(log.Topics[1].Bytes())
	token1 := common.BytesToAddress(log.Topics[2].Bytes())
	fee := new(big.Int).SetBytes(log.Topics[3].Bytes())

	// 解析data参数 (tickSpacing, pool)
	// tickSpacing := new(big.Int).SetBytes(log.Data[0:32])
	pool := common.BytesToAddress(log.Data[44:64]) // 地址在后20字节

	poolData := map[string]interface{}{
		"pool_address": pool.Hex(),
		"token0":       token0.Hex(),
		"token1":       token1.Hex(),
		"version":      "v3",
		"fee_tier":     fee.String(),
	}

	event := u.createBusinessEvent(tx, core.EventTypePoolCreated, poolData, eventIndex)
	return &event
}

// 辅助方法

// getToken0Address 获取池的token0地址
func (u *UniswapHandler) getToken0Address(poolAddress common.Address) string {
	if u.contractCaller == nil {
		// 如果没有合约调用器，返回占位符
		return "0x0000000000000000000000000000000000000000"
	}

	// 尝试作为V2池获取信息
	ctx := context.Background()
	if v2Info, err := u.contractCaller.GetCachedV2PairInfo(ctx, poolAddress); err == nil {
		return v2Info.Token0.Hex()
	}

	// 尝试作为V3池获取信息
	if v3Info, err := u.contractCaller.GetCachedV3PoolInfo(ctx, poolAddress); err == nil {
		return v3Info.Token0.Hex()
	}

	// 如果都失败，返回占位符
	return "0x0000000000000000000000000000000000000000"
}

// getToken1Address 获取池的token1地址
func (u *UniswapHandler) getToken1Address(poolAddress common.Address) string {
	if u.contractCaller == nil {
		// 如果没有合约调用器，返回占位符
		return "0x0000000000000000000000000000000000000001"
	}

	// 尝试作为V2池获取信息
	ctx := context.Background()
	if v2Info, err := u.contractCaller.GetCachedV2PairInfo(ctx, poolAddress); err == nil {
		return v2Info.Token1.Hex()
	}

	// 尝试作为V3池获取信息
	if v3Info, err := u.contractCaller.GetCachedV3PoolInfo(ctx, poolAddress); err == nil {
		return v3Info.Token1.Hex()
	}

	// 如果都失败，返回占位符
	return "0x0000000000000000000000000000000000000001"
}

// calculatePrice 计算交换价格
func (u *UniswapHandler) calculatePrice(amountIn, amountOut *big.Int) string {
	if amountIn.Cmp(big.NewInt(0)) == 0 {
		return "0"
	}

	// 价格 = amountOut / amountIn
	price := new(big.Float).Quo(
		new(big.Float).SetInt(amountOut),
		new(big.Float).SetInt(amountIn),
	)

	return price.String()
}

// calculateV2Fee 计算V2手续费 (固定0.3%)
func (u *UniswapHandler) calculateV2Fee(amountIn *big.Int) *big.Int {
	// V2手续费 = amountIn * 0.003
	fee := new(big.Int).Mul(amountIn, big.NewInt(3))
	fee.Div(fee, big.NewInt(1000))
	return fee
}

// calculateV3Fee 计算V3手续费 (动态费率)
func (u *UniswapHandler) calculateV3Fee(poolAddress common.Address, amountIn *big.Int) *big.Int {
	// 在实际实现中，这里应该调用合约的fee()方法获取费率
	// V3有不同的费率：500(0.05%), 3000(0.3%), 10000(1%)
	// 为了简化，这里假设是0.3%
	fee := new(big.Int).Mul(amountIn, big.NewInt(3))
	fee.Div(fee, big.NewInt(1000))
	return fee
}

// calculateLiquidityMinted 计算铸造的流动性代币数量
func (u *UniswapHandler) calculateLiquidityMinted(amount0, amount1 *big.Int) *big.Int {
	// 简化计算：使用几何平均数
	// 实际应该根据池的当前状态计算
	if amount0.Cmp(big.NewInt(0)) == 0 || amount1.Cmp(big.NewInt(0)) == 0 {
		return big.NewInt(0)
	}

	// sqrt(amount0 * amount1)
	product := new(big.Int).Mul(amount0, amount1)
	return new(big.Int).Sqrt(product)
}

// calculateLiquidityBurned 计算销毁的流动性代币数量
func (u *UniswapHandler) calculateLiquidityBurned(amount0, amount1 *big.Int) *big.Int {
	// 简化计算，实际应该根据池的状态计算
	return u.calculateLiquidityMinted(amount0, amount1)
}

// isUniswapContract 检查是否为Uniswap合约
func (u *UniswapHandler) isUniswapContract(address common.Address) bool {
	knownContracts := []common.Address{
		UniswapV2Router,
		UniswapV2Factory,
		UniswapV3Router,
		UniswapV3Router2,
		UniswapV3Factory,
	}

	for _, contract := range knownContracts {
		if address == contract {
			return true
		}
	}

	// 还可以检查是否为Uniswap池合约
	// 这需要更复杂的逻辑，比如检查合约代码或调用特定方法
	return false
}

// getPoolInfo 获取池信息 (缓存优化)
type PoolInfo struct {
	Token0  string
	Token1  string
	Fee     *big.Int
	Version string
}

var poolInfoCache = make(map[string]*PoolInfo)

func (u *UniswapHandler) getPoolInfo(poolAddress common.Address) *PoolInfo {
	addressStr := poolAddress.Hex()

	// 检查缓存
	if info, exists := poolInfoCache[addressStr]; exists {
		return info
	}

	// 在实际实现中，这里应该调用合约方法获取信息
	// 为了简化，返回默认信息
	info := &PoolInfo{
		Token0:  u.getToken0Address(poolAddress),
		Token1:  u.getToken1Address(poolAddress),
		Fee:     big.NewInt(3000), // 默认0.3%
		Version: "v2",
	}

	// 缓存信息
	poolInfoCache[addressStr] = info
	return info
}

// validateEventData 验证事件数据
func (u *UniswapHandler) validateEventData(data interface{}) bool {
	switch d := data.(type) {
	case *core.SwapEventData:
		return d.AmountIn != nil && d.AmountOut != nil &&
			d.AmountIn.Cmp(big.NewInt(0)) > 0 &&
			d.AmountOut.Cmp(big.NewInt(0)) > 0
	case *core.LiquidityEventData:
		return d.AmountA != nil && d.AmountB != nil &&
			d.AmountA.Cmp(big.NewInt(0)) > 0 &&
			d.AmountB.Cmp(big.NewInt(0)) > 0
	default:
		return true
	}
}
