package dex

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// PancakeSwapHandler PancakeSwap协议处理器
type PancakeSwapHandler struct {
	BaseProtocolHandler
	v2PairABI      abi.ABI
	v3PoolABI      abi.ABI
	routerV2ABI    abi.ABI
	routerV3ABI    abi.ABI
	contractCaller *PancakeContractCaller
}

// PancakeSwap合约地址
var (
	// PancakeSwap V2
	PancakeV2Router  = common.HexToAddress("******************************************")
	PancakeV2Factory = common.HexToAddress("******************************************")

	// PancakeSwap V3
	PancakeV3Router  = common.HexToAddress("******************************************")
	PancakeV3Factory = common.HexToAddress("******************************************")

	// 事件签名 (使用正确的Keccak256哈希)
	// V2 Swap(address,uint256,uint256,uint256,uint256,address)
	PancakeSwapV2EventSignature = common.HexToHash("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822")
	// V2 Mint(address,uint256,uint256)
	PancakeMintV2EventSignature = common.HexToHash("0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f")
	// V2 Burn(address,uint256,uint256,address)
	PancakeBurnV2EventSignature = common.HexToHash("0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496")
	// V2 PairCreated(address,address,address,uint256)
	PancakePairCreatedEventSignature = common.HexToHash("0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9")

	// V3 Router事件 - 这是我们在日志中看到的事件
	// SwapRouter的Swap事件签名 (可能是不同的事件结构)
	PancakeV3RouterSwapSignature = common.HexToHash("0x19b47279256b2a23a1665c810c8d55a1758940ee09377d4f8d26497a3577dc83")
)

// NewPancakeSwapHandler 创建PancakeSwap处理器
func NewPancakeSwapHandler() *PancakeSwapHandler {
	handler := &PancakeSwapHandler{
		BaseProtocolHandler: BaseProtocolHandler{
			protocolName: "PancakeSwap",
			supportedChains: []core.ChainType{
				core.ChainTypeBSC,
			},
		},
	}

	// 初始化ABI
	if err := handler.initABIs(); err != nil {
		log.Printf("⚠️ PancakeSwap ABI初始化失败: %v", err)
	}

	return handler
}

// initABIs 初始化ABI (复用Uniswap的ABI，因为接口兼容)
func (p *PancakeSwapHandler) initABIs() error {
	// V2 Pair ABI (与Uniswap V2兼容)
	v2PairABIJSON := `[
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0In", "type": "uint256"},
				{"indexed": false, "name": "amount1In", "type": "uint256"},
				{"indexed": false, "name": "amount0Out", "type": "uint256"},
				{"indexed": false, "name": "amount1Out", "type": "uint256"},
				{"indexed": true, "name": "to", "type": "address"}
			],
			"name": "Swap",
			"type": "event"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0", "type": "uint256"},
				{"indexed": false, "name": "amount1", "type": "uint256"}
			],
			"name": "Mint",
			"type": "event"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "sender", "type": "address"},
				{"indexed": false, "name": "amount0", "type": "uint256"},
				{"indexed": false, "name": "amount1", "type": "uint256"},
				{"indexed": true, "name": "to", "type": "address"}
			],
			"name": "Burn",
			"type": "event"
		}
	]`

	var err error
	p.v2PairABI, err = abi.JSON(strings.NewReader(v2PairABIJSON))
	if err != nil {
		return fmt.Errorf("解析V2 Pair ABI失败: %w", err)
	}

	return nil
}

// SetContractCaller 设置合约调用器
func (p *PancakeSwapHandler) SetContractCaller(caller *PancakeContractCaller) {
	p.contractCaller = caller
}

// SupportsTransaction 检查是否支持该交易
func (p *PancakeSwapHandler) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	// 检查链类型
	if tx.ChainType != core.ChainTypeBSC {
		return false
	}

	// 检查是否与PancakeSwap相关合约交互
	pancakeAddresses := []string{
		PancakeV2Router.Hex(),
		PancakeV2Factory.Hex(),
		PancakeV3Router.Hex(),
		PancakeV3Factory.Hex(),
		// 添加一些知名的PancakeSwap池地址
		"0x58F876857a02D6762E0101bb5C46A8c1ED44Dc16", // WBNB-BUSD
		"0x16b9a82891338f9bA80E2D6970FddA79D1eb0daE", // WBNB-USDT
		"0x0eD7e52944161450477ee417DE9Cd3a859b14fD0", // CAKE-WBNB
	}

	// 检查直接交互
	if p.isContractAddress(tx.ToAddress, pancakeAddresses) {
		return true
	}

	// 检查交易日志中是否包含PancakeSwap事件
	if p.containsPancakeSwapEvents(tx) {
		return true
	}

	return false
}

// containsPancakeSwapEvents 检查是否包含PancakeSwap事件
func (p *PancakeSwapHandler) containsPancakeSwapEvents(tx *core.UnifiedTransaction) bool {
	rawData, ok := tx.RawData.(map[string]interface{})
	if !ok {
		return false
	}

	receipt, ok := rawData["receipt"].(*types.Receipt)
	if !ok {
		return false
	}

	for _, log := range receipt.Logs {
		if len(log.Topics) == 0 {
			continue
		}

		// 检查是否为PancakeSwap事件签名
		eventSig := log.Topics[0]
		if eventSig == PancakeSwapV2EventSignature ||
			eventSig == PancakeMintV2EventSignature ||
			eventSig == PancakeBurnV2EventSignature ||
			eventSig == PancakePairCreatedEventSignature ||
			eventSig == PancakeV3RouterSwapSignature {
			return true
		}
	}

	return false
}

// ExtractEvents 提取事件
func (p *PancakeSwapHandler) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	var events []core.BusinessEvent

	rawData, ok := tx.RawData.(map[string]interface{})
	if !ok {
		return events, nil
	}

	receipt, ok := rawData["receipt"].(*types.Receipt)
	if !ok {
		return events, nil
	}

	eventIndex := 0
	for _, eventLog := range receipt.Logs {
		if len(eventLog.Topics) == 0 {
			continue
		}

		eventSig := eventLog.Topics[0]
		var businessEvent *core.BusinessEvent

		switch eventSig {
		case PancakeSwapV2EventSignature:
			businessEvent = p.parseV2SwapEvent(eventLog, tx, eventIndex)
		case PancakeMintV2EventSignature:
			businessEvent = p.parseV2MintEvent(eventLog, tx, eventIndex)
		case PancakeBurnV2EventSignature:
			businessEvent = p.parseV2BurnEvent(eventLog, tx, eventIndex)
		case PancakePairCreatedEventSignature:
			businessEvent = p.parsePairCreatedEvent(eventLog, tx, eventIndex)
		case PancakeV3RouterSwapSignature:
			businessEvent = p.parseV3RouterSwapEvent(eventLog, tx, eventIndex)
		default:
			// 不匹配的事件，跳过
			continue
		}

		if businessEvent != nil {
			events = append(events, *businessEvent)
			eventIndex++
		}
	}

	return events, nil
}

// parseV2SwapEvent 解析V2交换事件
func (p *PancakeSwapHandler) parseV2SwapEvent(eventLog *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(eventLog.Topics) < 3 || len(eventLog.Data) < 128 {
		return nil
	}

	// 解析indexed参数
	sender := common.BytesToAddress(eventLog.Topics[1].Bytes())
	to := common.BytesToAddress(eventLog.Topics[2].Bytes())

	// 解析data参数
	amount0In := new(big.Int).SetBytes(eventLog.Data[0:32])
	amount1In := new(big.Int).SetBytes(eventLog.Data[32:64])
	amount0Out := new(big.Int).SetBytes(eventLog.Data[64:96])
	amount1Out := new(big.Int).SetBytes(eventLog.Data[96:128])

	// 确定输入和输出代币
	var tokenIn, tokenOut string
	var amountIn, amountOut *big.Int

	if amount0In.Cmp(big.NewInt(0)) > 0 {
		tokenIn = p.getToken0Address(eventLog.Address)
		tokenOut = p.getToken1Address(eventLog.Address)
		amountIn = amount0In
		amountOut = amount1Out
	} else {
		tokenIn = p.getToken1Address(eventLog.Address)
		tokenOut = p.getToken0Address(eventLog.Address)
		amountIn = amount1In
		amountOut = amount0Out
	}

	swapData := &core.SwapEventData{
		PoolID:    eventLog.Address.Hex(),
		TokenIn:   tokenIn,
		TokenOut:  tokenOut,
		AmountIn:  amountIn,
		AmountOut: amountOut,
		Sender:    utils.NormalizeAddress(sender.Hex()),
		Recipient: utils.NormalizeAddress(to.Hex()),
		Price:     p.calculatePrice(amountIn, amountOut),
		FeePaid:   p.calculateV2Fee(amountIn), // PancakeSwap V2是0.25%手续费
	}

	event := p.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex)
	return &event
}

// parseV2MintEvent 解析V2添加流动性事件
func (p *PancakeSwapHandler) parseV2MintEvent(eventLog *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(eventLog.Topics) < 2 || len(eventLog.Data) < 64 {
		return nil
	}

	sender := common.BytesToAddress(eventLog.Topics[1].Bytes())
	amount0 := new(big.Int).SetBytes(eventLog.Data[0:32])
	amount1 := new(big.Int).SetBytes(eventLog.Data[32:64])

	liquidityData := &core.LiquidityEventData{
		PoolID:          eventLog.Address.Hex(),
		TokenA:          p.getToken0Address(eventLog.Address),
		TokenB:          p.getToken1Address(eventLog.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityMinted: p.calculateLiquidityMinted(amount0, amount1),
		Provider:        utils.NormalizeAddress(sender.Hex()),
	}

	event := p.createBusinessEvent(tx, core.EventTypeAddLiquidity, liquidityData, eventIndex)
	return &event
}

// parseV2BurnEvent 解析V2移除流动性事件
func (p *PancakeSwapHandler) parseV2BurnEvent(eventLog *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(eventLog.Topics) < 3 || len(eventLog.Data) < 64 {
		return nil
	}

	sender := common.BytesToAddress(eventLog.Topics[1].Bytes())
	to := common.BytesToAddress(eventLog.Topics[2].Bytes())
	amount0 := new(big.Int).SetBytes(eventLog.Data[0:32])
	amount1 := new(big.Int).SetBytes(eventLog.Data[32:64])

	liquidityData := &core.LiquidityEventData{
		PoolID:          eventLog.Address.Hex(),
		TokenA:          p.getToken0Address(eventLog.Address),
		TokenB:          p.getToken1Address(eventLog.Address),
		AmountA:         amount0,
		AmountB:         amount1,
		LiquidityBurned: p.calculateLiquidityBurned(amount0, amount1),
		Provider:        utils.NormalizeAddress(to.Hex()),
	}

	if !strings.EqualFold(sender.Hex(), to.Hex()) {
		liquidityData.Provider = utils.NormalizeAddress(sender.Hex())
	}

	event := p.createBusinessEvent(tx, core.EventTypeRemoveLiquidity, liquidityData, eventIndex)
	return &event
}

// parsePairCreatedEvent 解析配对创建事件
func (p *PancakeSwapHandler) parsePairCreatedEvent(eventLog *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	if len(eventLog.Topics) < 3 || len(eventLog.Data) < 64 {
		return nil
	}

	token0 := common.BytesToAddress(eventLog.Topics[1].Bytes())
	token1 := common.BytesToAddress(eventLog.Topics[2].Bytes())
	pair := common.BytesToAddress(eventLog.Data[12:32])

	poolData := map[string]interface{}{
		"pool_address": pair.Hex(),
		"token0":       token0.Hex(),
		"token1":       token1.Hex(),
		"version":      "v2",
		"fee_tier":     "2500", // PancakeSwap V2是0.25%
	}

	event := p.createBusinessEvent(tx, core.EventTypePoolCreated, poolData, eventIndex)
	return &event
}

// parseV3RouterSwapEvent 解析V3 Router交换事件
func (p *PancakeSwapHandler) parseV3RouterSwapEvent(eventLog *types.Log, tx *core.UnifiedTransaction, eventIndex int) *core.BusinessEvent {
	// V3 Router事件的结构可能不同，我们先尝试基本解析
	// 暂时创建一个基本的交换事件，使用可用的信息
	swapData := &core.SwapEventData{
		PoolID:    eventLog.Address.Hex(), // Router地址
		TokenIn:   "unknown",              // 需要从交易数据中解析
		TokenOut:  "unknown",              // 需要从交易数据中解析
		AmountIn:  big.NewInt(0),          // 需要从交易数据中解析
		AmountOut: big.NewInt(0),          // 需要从交易数据中解析
		Sender:    tx.FromAddress,         // 使用交易发送者
		Recipient: tx.FromAddress,         // 使用交易发送者
		Price:     "0",
		FeePaid:   big.NewInt(0),
	}

	event := p.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex)
	return &event
}

// 辅助方法

// getToken0Address 获取池的token0地址
func (p *PancakeSwapHandler) getToken0Address(poolAddress common.Address) string {
	if p.contractCaller == nil {
		return "0x0000000000000000000000000000000000000000"
	}

	ctx := context.Background()
	if v2Info, err := p.contractCaller.GetCachedV2PairInfo(ctx, poolAddress); err == nil {
		return v2Info.Token0.Hex()
	}

	return "0x0000000000000000000000000000000000000000"
}

// getToken1Address 获取池的token1地址
func (p *PancakeSwapHandler) getToken1Address(poolAddress common.Address) string {
	if p.contractCaller == nil {
		return "0x0000000000000000000000000000000000000001"
	}

	ctx := context.Background()
	if v2Info, err := p.contractCaller.GetCachedV2PairInfo(ctx, poolAddress); err == nil {
		return v2Info.Token1.Hex()
	}

	return "0x0000000000000000000000000000000000000001"
}

// calculatePrice 计算交换价格
func (p *PancakeSwapHandler) calculatePrice(amountIn, amountOut *big.Int) string {
	if amountIn.Cmp(big.NewInt(0)) == 0 {
		return "0"
	}

	price := new(big.Float).Quo(
		new(big.Float).SetInt(amountOut),
		new(big.Float).SetInt(amountIn),
	)

	return price.String()
}

// calculateV2Fee 计算V2手续费 (PancakeSwap是0.25%)
func (p *PancakeSwapHandler) calculateV2Fee(amountIn *big.Int) *big.Int {
	fee := new(big.Int).Mul(amountIn, big.NewInt(25))
	fee.Div(fee, big.NewInt(10000))
	return fee
}

// calculateLiquidityMinted 计算铸造的流动性代币数量
func (p *PancakeSwapHandler) calculateLiquidityMinted(amount0, amount1 *big.Int) *big.Int {
	if amount0.Cmp(big.NewInt(0)) == 0 || amount1.Cmp(big.NewInt(0)) == 0 {
		return big.NewInt(0)
	}

	product := new(big.Int).Mul(amount0, amount1)
	return new(big.Int).Sqrt(product)
}

// calculateLiquidityBurned 计算销毁的流动性代币数量
func (p *PancakeSwapHandler) calculateLiquidityBurned(amount0, amount1 *big.Int) *big.Int {
	return p.calculateLiquidityMinted(amount0, amount1)
}
