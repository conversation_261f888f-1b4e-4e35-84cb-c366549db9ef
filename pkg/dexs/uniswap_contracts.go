package dex

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// UniswapContractCaller Uniswap合约调用器
type UniswapContractCaller struct {
	client     *ethclient.Client
	v2PairABI  abi.ABI
	v3PoolABI  abi.ABI
	factoryABI abi.ABI
}

// NewUniswapContractCaller 创建合约调用器
func NewUniswapContractCaller(client *ethclient.Client) (*UniswapContractCaller, error) {
	caller := &UniswapContractCaller{
		client: client,
	}

	// 初始化ABI
	if err := caller.initABIs(); err != nil {
		return nil, fmt.Errorf("初始化ABI失败: %w", err)
	}

	return caller, nil
}

// initABIs 初始化合约ABI
func (u *UniswapContractCaller) initABIs() error {
	// Uniswap V2 Pair ABI (简化版)
	v2PairABIJSON := `[
		{
			"constant": true,
			"inputs": [],
			"name": "token0",
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "token1", 
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "getReserves",
			"outputs": [
				{"name": "_reserve0", "type": "uint112"},
				{"name": "_reserve1", "type": "uint112"},
				{"name": "_blockTimestampLast", "type": "uint32"}
			],
			"type": "function"
		}
	]`

	// Uniswap V3 Pool ABI (简化版)
	v3PoolABIJSON := `[
		{
			"constant": true,
			"inputs": [],
			"name": "token0",
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "token1",
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "fee",
			"outputs": [{"name": "", "type": "uint24"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "slot0",
			"outputs": [
				{"name": "sqrtPriceX96", "type": "uint160"},
				{"name": "tick", "type": "int24"},
				{"name": "observationIndex", "type": "uint16"},
				{"name": "observationCardinality", "type": "uint16"},
				{"name": "observationCardinalityNext", "type": "uint16"},
				{"name": "feeProtocol", "type": "uint8"},
				{"name": "unlocked", "type": "bool"}
			],
			"type": "function"
		}
	]`

	// Factory ABI (简化版)
	factoryABIJSON := `[
		{
			"constant": true,
			"inputs": [
				{"name": "tokenA", "type": "address"},
				{"name": "tokenB", "type": "address"}
			],
			"name": "getPair",
			"outputs": [{"name": "pair", "type": "address"}],
			"type": "function"
		}
	]`

	var err error

	// 解析V2 Pair ABI
	u.v2PairABI, err = abi.JSON(strings.NewReader(v2PairABIJSON))
	if err != nil {
		return fmt.Errorf("解析V2 Pair ABI失败: %w", err)
	}

	// 解析V3 Pool ABI
	u.v3PoolABI, err = abi.JSON(strings.NewReader(v3PoolABIJSON))
	if err != nil {
		return fmt.Errorf("解析V3 Pool ABI失败: %w", err)
	}

	// 解析Factory ABI
	u.factoryABI, err = abi.JSON(strings.NewReader(factoryABIJSON))
	if err != nil {
		return fmt.Errorf("解析Factory ABI失败: %w", err)
	}

	return nil
}

// GetV2PairInfo 获取V2池信息
func (u *UniswapContractCaller) GetV2PairInfo(ctx context.Context, pairAddress common.Address) (*V2PairInfo, error) {
	contract := bind.NewBoundContract(pairAddress, u.v2PairABI, u.client, nil, nil)

	// 获取token0
	var token0 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token0}, "token0"); err != nil {
		return nil, fmt.Errorf("获取token0失败: %w", err)
	}

	// 获取token1
	var token1 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token1}, "token1"); err != nil {
		return nil, fmt.Errorf("获取token1失败: %w", err)
	}

	// 获取储备量
	var reserves struct {
		Reserve0           *big.Int
		Reserve1           *big.Int
		BlockTimestampLast uint32
	}
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&reserves.Reserve0, &reserves.Reserve1, &reserves.BlockTimestampLast}, "getReserves"); err != nil {
		return nil, fmt.Errorf("获取储备量失败: %w", err)
	}

	return &V2PairInfo{
		PairAddress: pairAddress,
		Token0:      token0,
		Token1:      token1,
		Reserve0:    reserves.Reserve0,
		Reserve1:    reserves.Reserve1,
	}, nil
}

// GetV3PoolInfo 获取V3池信息
func (u *UniswapContractCaller) GetV3PoolInfo(ctx context.Context, poolAddress common.Address) (*V3PoolInfo, error) {
	contract := bind.NewBoundContract(poolAddress, u.v3PoolABI, u.client, nil, nil)

	// 获取token0
	var token0 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token0}, "token0"); err != nil {
		return nil, fmt.Errorf("获取token0失败: %w", err)
	}

	// 获取token1
	var token1 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token1}, "token1"); err != nil {
		return nil, fmt.Errorf("获取token1失败: %w", err)
	}

	// 获取费率
	var fee *big.Int
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&fee}, "fee"); err != nil {
		return nil, fmt.Errorf("获取费率失败: %w", err)
	}

	// 获取slot0信息
	var slot0 struct {
		SqrtPriceX96               *big.Int
		Tick                       *big.Int
		ObservationIndex           uint16
		ObservationCardinality     uint16
		ObservationCardinalityNext uint16
		FeeProtocol                uint8
		Unlocked                   bool
	}
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{
		&slot0.SqrtPriceX96,
		&slot0.Tick,
		&slot0.ObservationIndex,
		&slot0.ObservationCardinality,
		&slot0.ObservationCardinalityNext,
		&slot0.FeeProtocol,
		&slot0.Unlocked,
	}, "slot0"); err != nil {
		return nil, fmt.Errorf("获取slot0失败: %w", err)
	}

	return &V3PoolInfo{
		PoolAddress:  poolAddress,
		Token0:       token0,
		Token1:       token1,
		Fee:          fee,
		SqrtPriceX96: slot0.SqrtPriceX96,
		Tick:         slot0.Tick,
	}, nil
}

// GetPairAddress 从Factory获取配对地址
func (u *UniswapContractCaller) GetPairAddress(ctx context.Context, factoryAddress, tokenA, tokenB common.Address) (common.Address, error) {
	contract := bind.NewBoundContract(factoryAddress, u.factoryABI, u.client, nil, nil)

	var pairAddress common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&pairAddress}, "getPair", tokenA, tokenB); err != nil {
		return common.Address{}, fmt.Errorf("获取配对地址失败: %w", err)
	}

	return pairAddress, nil
}

// 数据结构定义

// V2PairInfo V2池信息
type V2PairInfo struct {
	PairAddress common.Address
	Token0      common.Address
	Token1      common.Address
	Reserve0    *big.Int
	Reserve1    *big.Int
}

// V3PoolInfo V3池信息
type V3PoolInfo struct {
	PoolAddress  common.Address
	Token0       common.Address
	Token1       common.Address
	Fee          *big.Int
	SqrtPriceX96 *big.Int
	Tick         *big.Int
}

// 缓存管理
type ContractInfoCache struct {
	v2Pairs map[string]*V2PairInfo
	v3Pools map[string]*V3PoolInfo
}

var contractCache = &ContractInfoCache{
	v2Pairs: make(map[string]*V2PairInfo),
	v3Pools: make(map[string]*V3PoolInfo),
}

// GetCachedV2PairInfo 获取缓存的V2池信息
func (u *UniswapContractCaller) GetCachedV2PairInfo(ctx context.Context, pairAddress common.Address) (*V2PairInfo, error) {
	addressStr := pairAddress.Hex()

	// 检查缓存
	if info, exists := contractCache.v2Pairs[addressStr]; exists {
		return info, nil
	}

	// 从合约获取信息
	info, err := u.GetV2PairInfo(ctx, pairAddress)
	if err != nil {
		return nil, err
	}

	// 缓存信息
	contractCache.v2Pairs[addressStr] = info
	return info, nil
}

// GetCachedV3PoolInfo 获取缓存的V3池信息
func (u *UniswapContractCaller) GetCachedV3PoolInfo(ctx context.Context, poolAddress common.Address) (*V3PoolInfo, error) {
	addressStr := poolAddress.Hex()

	// 检查缓存
	if info, exists := contractCache.v3Pools[addressStr]; exists {
		return info, nil
	}

	// 从合约获取信息
	info, err := u.GetV3PoolInfo(ctx, poolAddress)
	if err != nil {
		return nil, err
	}

	// 缓存信息
	contractCache.v3Pools[addressStr] = info
	return info, nil
}
