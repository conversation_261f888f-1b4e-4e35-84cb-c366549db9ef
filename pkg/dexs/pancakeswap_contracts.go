package dex

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// PancakeContractCaller PancakeSwap合约调用器
type PancakeContractCaller struct {
	client     *ethclient.Client
	v2PairABI  abi.ABI
	factoryABI abi.ABI
}

// NewPancakeContractCaller 创建PancakeSwap合约调用器
func NewPancakeContractCaller(client *ethclient.Client) (*PancakeContractCaller, error) {
	caller := &PancakeContractCaller{
		client: client,
	}

	// 初始化ABI
	if err := caller.initABIs(); err != nil {
		return nil, fmt.Errorf("初始化ABI失败: %w", err)
	}

	return caller, nil
}

// initABIs 初始化合约ABI
func (p *PancakeContractCaller) initABIs() error {
	// PancakeSwap V2 Pair ABI (与Uniswap兼容)
	v2PairABIJSON := `[
		{
			"constant": true,
			"inputs": [],
			"name": "token0",
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "token1", 
			"outputs": [{"name": "", "type": "address"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "getReserves",
			"outputs": [
				{"name": "_reserve0", "type": "uint112"},
				{"name": "_reserve1", "type": "uint112"},
				{"name": "_blockTimestampLast", "type": "uint32"}
			],
			"type": "function"
		}
	]`

	// Factory ABI
	factoryABIJSON := `[
		{
			"constant": true,
			"inputs": [
				{"name": "tokenA", "type": "address"},
				{"name": "tokenB", "type": "address"}
			],
			"name": "getPair",
			"outputs": [{"name": "pair", "type": "address"}],
			"type": "function"
		}
	]`

	var err error

	// 解析V2 Pair ABI
	p.v2PairABI, err = abi.JSON(strings.NewReader(v2PairABIJSON))
	if err != nil {
		return fmt.Errorf("解析V2 Pair ABI失败: %w", err)
	}

	// 解析Factory ABI
	p.factoryABI, err = abi.JSON(strings.NewReader(factoryABIJSON))
	if err != nil {
		return fmt.Errorf("解析Factory ABI失败: %w", err)
	}

	return nil
}

// GetV2PairInfo 获取V2池信息
func (p *PancakeContractCaller) GetV2PairInfo(ctx context.Context, pairAddress common.Address) (*V2PairInfo, error) {
	contract := bind.NewBoundContract(pairAddress, p.v2PairABI, p.client, nil, nil)

	// 获取token0
	var token0 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token0}, "token0"); err != nil {
		return nil, fmt.Errorf("获取token0失败: %w", err)
	}

	// 获取token1
	var token1 common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&token1}, "token1"); err != nil {
		return nil, fmt.Errorf("获取token1失败: %w", err)
	}

	// 获取储备量
	var reserves struct {
		Reserve0           *big.Int
		Reserve1           *big.Int
		BlockTimestampLast uint32
	}
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&reserves.Reserve0, &reserves.Reserve1, &reserves.BlockTimestampLast}, "getReserves"); err != nil {
		return nil, fmt.Errorf("获取储备量失败: %w", err)
	}

	return &V2PairInfo{
		PairAddress: pairAddress,
		Token0:      token0,
		Token1:      token1,
		Reserve0:    reserves.Reserve0,
		Reserve1:    reserves.Reserve1,
	}, nil
}

// GetPairAddress 从Factory获取配对地址
func (p *PancakeContractCaller) GetPairAddress(ctx context.Context, factoryAddress, tokenA, tokenB common.Address) (common.Address, error) {
	contract := bind.NewBoundContract(factoryAddress, p.factoryABI, p.client, nil, nil)

	var pairAddress common.Address
	if err := contract.Call(&bind.CallOpts{Context: ctx}, &[]interface{}{&pairAddress}, "getPair", tokenA, tokenB); err != nil {
		return common.Address{}, fmt.Errorf("获取配对地址失败: %w", err)
	}

	return pairAddress, nil
}

// 缓存管理
var pancakeContractCache = &ContractInfoCache{
	v2Pairs: make(map[string]*V2PairInfo),
	v3Pools: make(map[string]*V3PoolInfo),
}

// GetCachedV2PairInfo 获取缓存的V2池信息
func (p *PancakeContractCaller) GetCachedV2PairInfo(ctx context.Context, pairAddress common.Address) (*V2PairInfo, error) {
	addressStr := pairAddress.Hex()

	// 检查缓存
	if info, exists := pancakeContractCache.v2Pairs[addressStr]; exists {
		return info, nil
	}

	// 从合约获取信息
	info, err := p.GetV2PairInfo(ctx, pairAddress)
	if err != nil {
		return nil, err
	}

	// 缓存信息
	pancakeContractCache.v2Pairs[addressStr] = info
	return info, nil
}

// HealthCheck 健康检查
func (p *PancakeContractCaller) HealthCheck() error {
	ctx := context.Background()

	// 测试获取一个已知的PancakeSwap配对信息
	// WBNB-BUSD配对地址
	wbnbBusdPair := common.HexToAddress("0x58F876857a02D6762E0101bb5C46A8c1ED44Dc16")

	_, err := p.GetV2PairInfo(ctx, wbnbBusdPair)
	if err != nil {
		return fmt.Errorf("PancakeSwap合约调用器健康检查失败: %w", err)
	}

	return nil
}
