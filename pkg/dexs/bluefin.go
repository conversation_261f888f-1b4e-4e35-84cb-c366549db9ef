package dex

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/big"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"
)

// Bluefin协议常量
const (
	// Bluefin AMM合约地址
	BluefinAmmAddr = "0x3492c874c1e3b3e2984e8c41b589e642d4d0a5d6459e5a9cfc2d52fd7c89c267"

	// Bluefin事件类型
	BluefinPoolCreatedEventType     = BluefinAmmAddr + "::events::PoolCreated"
	BluefinAddLiquidityEventType    = BluefinAmmAddr + "::events::LiquidityProvided"
	BluefinRemoveLiquidityEventType = BluefinAmmAddr + "::events::LiquidityRemoved"
	BluefinAssetSwapEventType       = BluefinAmmAddr + "::events::AssetSwap"
	BluefinFlashSwapEventType       = BluefinAmmAddr + "::events::FlashSwap"
)

// BluefinHandler Bluefin协议处理器
type BluefinHandler struct {
	BaseProtocolHandler
}

// NewBluefinHandler 创建Bluefin处理器
func NewBluefinHandler() *BluefinHandler {
	return &BluefinHandler{
		BaseProtocolHandler: BaseProtocolHandler{
			protocolName:    "Bluefin",
			supportedChains: []core.ChainType{core.ChainTypeSui},
		},
	}
}

// SupportsTransaction 检查是否支持该交易
func (b *BluefinHandler) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	// 只支持Sui链
	if tx.ChainType != core.ChainTypeSui {
		return false
	}

	// 检查是否包含Bluefin相关事件
	return b.containsBluefinEvents(tx)
}

// ExtractEvents 提取事件
func (b *BluefinHandler) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	var events []core.BusinessEvent

	// 从原始数据中提取Sui事件
	suiEvents, err := b.extractSuiEvents(tx)
	if err != nil {
		return nil, fmt.Errorf("提取Sui事件失败: %w", err)
	}

	eventIndex := 0
	for _, suiEvent := range suiEvents {
		// 解析Bluefin事件
		businessEvents, err := b.parseBluefinEvent(tx, suiEvent, &eventIndex)
		if err != nil {
			log.Printf("⚠️ 解析Bluefin事件失败: %v", err)
			continue
		}

		events = append(events, businessEvents...)
	}

	return events, nil
}

// containsBluefinEvents 检查是否包含Bluefin事件
func (b *BluefinHandler) containsBluefinEvents(tx *core.UnifiedTransaction) bool {
	suiEvents, err := b.extractSuiEvents(tx)
	if err != nil {
		return false
	}

	for _, event := range suiEvents {
		if b.isBluefinEvent(event) {
			return true
		}
	}

	return false
}

// extractSuiEvents 从交易中提取Sui事件
func (b *BluefinHandler) extractSuiEvents(tx *core.UnifiedTransaction) ([]map[string]interface{}, error) {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 如果是map格式，尝试获取events字段
		if events, ok := rawData["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	default:
		// 尝试通过JSON解析
		data, err := json.Marshal(rawData)
		if err != nil {
			return nil, fmt.Errorf("序列化原始数据失败: %w", err)
		}

		var result map[string]interface{}
		if err := json.Unmarshal(data, &result); err != nil {
			return nil, fmt.Errorf("反序列化原始数据失败: %w", err)
		}

		if events, ok := result["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	}

	return nil, nil
}

// parseEventsFromInterface 从interface{}解析事件
func (b *BluefinHandler) parseEventsFromInterface(events interface{}) ([]map[string]interface{}, error) {
	switch eventsData := events.(type) {
	case []interface{}:
		result := make([]map[string]interface{}, 0, len(eventsData))
		for _, event := range eventsData {
			if eventMap, ok := event.(map[string]interface{}); ok {
				result = append(result, eventMap)
			}
		}
		return result, nil
	case []map[string]interface{}:
		return eventsData, nil
	default:
		return nil, fmt.Errorf("无法解析事件数据类型: %T", events)
	}
}

// isBluefinEvent 检查是否是Bluefin事件
func (b *BluefinHandler) isBluefinEvent(event map[string]interface{}) bool {
	// 检查事件类型
	if eventType, ok := event["type"].(string); ok {
		// Bluefin精确事件类型匹配
		bluefinEventTypes := []string{
			BluefinPoolCreatedEventType,
			BluefinAddLiquidityEventType,
			BluefinRemoveLiquidityEventType,
			BluefinAssetSwapEventType,
			BluefinFlashSwapEventType,
		}

		for _, bluefinType := range bluefinEventTypes {
			if eventType == bluefinType {
				return true
			}
		}
	}

	return false
}

// parseBluefinEvent 解析Bluefin事件
func (b *BluefinHandler) parseBluefinEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex *int) ([]core.BusinessEvent, error) {
	var events []core.BusinessEvent

	eventType, ok := event["type"].(string)
	if !ok {
		return nil, fmt.Errorf("事件类型不存在")
	}

	// 根据精确的事件类型解析
	switch eventType {
	case BluefinAssetSwapEventType:
		swapEvent, err := b.parseSwapEvent(tx, event, *eventIndex)
		if err == nil {
			events = append(events, swapEvent)
			*eventIndex++
		}
	case BluefinFlashSwapEventType:
		// FlashSwap也作为交换事件处理
		swapEvent, err := b.parseFlashSwapEvent(tx, event, *eventIndex)
		if err == nil {
			events = append(events, swapEvent)
			*eventIndex++
		}
	case BluefinAddLiquidityEventType:
		liquidityEvent, err := b.parseAddLiquidityEvent(tx, event, *eventIndex)
		if err == nil {
			events = append(events, liquidityEvent)
			*eventIndex++
		}
	case BluefinRemoveLiquidityEventType:
		liquidityEvent, err := b.parseRemoveLiquidityEvent(tx, event, *eventIndex)
		if err == nil {
			events = append(events, liquidityEvent)
			*eventIndex++
		}
	case BluefinPoolCreatedEventType:
		poolEvent, err := b.parsePoolCreatedEvent(tx, event, *eventIndex)
		if err == nil {
			events = append(events, poolEvent)
			*eventIndex++
		}
	}

	return events, nil
}

// parseSwapEvent 解析交换事件
func (b *BluefinHandler) parseSwapEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex int) (core.BusinessEvent, error) {
	parsedData, ok := event["parsedJson"].(map[string]interface{})
	if !ok {
		return core.BusinessEvent{}, fmt.Errorf("解析事件数据不存在")
	}

	// 提取交换数据
	swapData := &core.SwapEventData{
		Sender:    tx.FromAddress,
		Recipient: tx.FromAddress, // 在Sui中，发送者通常也是接收者
	}

	// 解析具体字段
	if poolId, ok := parsedData["poolId"].(string); ok {
		swapData.PoolID = poolId
	}

	if tokenIn, ok := parsedData["tokenIn"].(string); ok {
		swapData.TokenIn = tokenIn
	}

	if tokenOut, ok := parsedData["tokenOut"].(string); ok {
		swapData.TokenOut = tokenOut
	}

	if amountIn, ok := parsedData["amountIn"].(string); ok {
		if amount, err := utils.BigIntFromString(amountIn); err == nil {
			swapData.AmountIn = amount
		}
	}

	if amountOut, ok := parsedData["amountOut"].(string); ok {
		if amount, err := utils.BigIntFromString(amountOut); err == nil {
			swapData.AmountOut = amount
		}
	}

	// 计算价格 (如果有输入输出金额)
	if swapData.AmountIn != nil && swapData.AmountOut != nil &&
		swapData.AmountIn.Cmp(big.NewInt(0)) > 0 {
		// 价格 = AmountOut / AmountIn
		price := new(big.Float).Quo(
			new(big.Float).SetInt(swapData.AmountOut),
			new(big.Float).SetInt(swapData.AmountIn),
		)
		swapData.Price = price.String()
	}

	return b.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex), nil
}

// parseAddLiquidityEvent 解析添加流动性事件
func (b *BluefinHandler) parseAddLiquidityEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex int) (core.BusinessEvent, error) {
	parsedData, ok := event["parsedJson"].(map[string]interface{})
	if !ok {
		return core.BusinessEvent{}, fmt.Errorf("解析事件数据不存在")
	}

	liquidityData := &core.LiquidityEventData{
		Provider: tx.FromAddress,
	}

	// 解析具体字段
	if poolId, ok := parsedData["poolId"].(string); ok {
		liquidityData.PoolID = poolId
	}

	if tokenA, ok := parsedData["tokenA"].(string); ok {
		liquidityData.TokenA = tokenA
	}

	if tokenB, ok := parsedData["tokenB"].(string); ok {
		liquidityData.TokenB = tokenB
	}

	if amountA, ok := parsedData["amountA"].(string); ok {
		if amount, err := utils.BigIntFromString(amountA); err == nil {
			liquidityData.AmountA = amount
		}
	}

	if amountB, ok := parsedData["amountB"].(string); ok {
		if amount, err := utils.BigIntFromString(amountB); err == nil {
			liquidityData.AmountB = amount
		}
	}

	if liquidity, ok := parsedData["liquidity"].(string); ok {
		if amount, err := utils.BigIntFromString(liquidity); err == nil {
			liquidityData.LiquidityMinted = amount
		}
	}

	return b.createBusinessEvent(tx, core.EventTypeAddLiquidity, liquidityData, eventIndex), nil
}

// parseRemoveLiquidityEvent 解析移除流动性事件
func (b *BluefinHandler) parseRemoveLiquidityEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex int) (core.BusinessEvent, error) {
	parsedData, ok := event["parsedJson"].(map[string]interface{})
	if !ok {
		return core.BusinessEvent{}, fmt.Errorf("解析事件数据不存在")
	}

	liquidityData := &core.LiquidityEventData{
		Provider: tx.FromAddress,
	}

	// 解析具体字段
	if poolId, ok := parsedData["poolId"].(string); ok {
		liquidityData.PoolID = poolId
	}

	if tokenA, ok := parsedData["tokenA"].(string); ok {
		liquidityData.TokenA = tokenA
	}

	if tokenB, ok := parsedData["tokenB"].(string); ok {
		liquidityData.TokenB = tokenB
	}

	if amountA, ok := parsedData["amountA"].(string); ok {
		if amount, err := utils.BigIntFromString(amountA); err == nil {
			liquidityData.AmountA = amount
		}
	}

	if amountB, ok := parsedData["amountB"].(string); ok {
		if amount, err := utils.BigIntFromString(amountB); err == nil {
			liquidityData.AmountB = amount
		}
	}

	if liquidity, ok := parsedData["liquidity"].(string); ok {
		if amount, err := utils.BigIntFromString(liquidity); err == nil {
			liquidityData.LiquidityBurned = amount
		}
	}

	return b.createBusinessEvent(tx, core.EventTypeRemoveLiquidity, liquidityData, eventIndex), nil
}

// parsePoolCreatedEvent 解析池创建事件
func (b *BluefinHandler) parsePoolCreatedEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex int) (core.BusinessEvent, error) {
	parsedData, ok := event["parsedJson"].(map[string]interface{})
	if !ok {
		return core.BusinessEvent{}, fmt.Errorf("解析事件数据不存在")
	}

	// 池创建事件数据结构可以根据需要定义
	poolData := map[string]interface{}{
		"creator": tx.FromAddress,
	}

	// 复制解析的数据
	for key, value := range parsedData {
		poolData[key] = value
	}

	return b.createBusinessEvent(tx, core.EventTypePoolCreated, poolData, eventIndex), nil
}

// parseFlashSwapEvent 解析闪电交换事件
func (b *BluefinHandler) parseFlashSwapEvent(tx *core.UnifiedTransaction, event map[string]interface{}, eventIndex int) (core.BusinessEvent, error) {
	parsedData, ok := event["parsedJson"].(map[string]interface{})
	if !ok {
		return core.BusinessEvent{}, fmt.Errorf("解析事件数据不存在")
	}

	// 提取闪电交换数据
	swapData := &core.SwapEventData{
		Sender:    tx.FromAddress,
		Recipient: tx.FromAddress, // 在Sui中，发送者通常也是接收者
	}

	// 解析具体字段
	if poolId, ok := parsedData["poolId"].(string); ok {
		swapData.PoolID = poolId
	}

	if tokenIn, ok := parsedData["tokenIn"].(string); ok {
		swapData.TokenIn = tokenIn
	}

	if tokenOut, ok := parsedData["tokenOut"].(string); ok {
		swapData.TokenOut = tokenOut
	}

	if amountIn, ok := parsedData["amountIn"].(string); ok {
		if amount, err := utils.BigIntFromString(amountIn); err == nil {
			swapData.AmountIn = amount
		}
	}

	if amountOut, ok := parsedData["amountOut"].(string); ok {
		if amount, err := utils.BigIntFromString(amountOut); err == nil {
			swapData.AmountOut = amount
		}
	}

	// 计算价格 (如果有输入输出金额)
	if swapData.AmountIn != nil && swapData.AmountOut != nil &&
		swapData.AmountIn.Cmp(big.NewInt(0)) > 0 {
		// 价格 = AmountOut / AmountIn
		price := new(big.Float).Quo(
			new(big.Float).SetInt(swapData.AmountOut),
			new(big.Float).SetInt(swapData.AmountIn),
		)
		swapData.Price = price.String()
	}

	return b.createBusinessEvent(tx, core.EventTypeSwap, swapData, eventIndex), nil
}
