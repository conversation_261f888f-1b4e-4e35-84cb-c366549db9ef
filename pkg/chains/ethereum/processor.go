package ethereum

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"time"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

// EthereumProcessor 以太坊链处理器
type EthereumProcessor struct {
	client    *ethclient.Client
	chainID   *big.Int
	chainType core.ChainType
	config    *EthereumConfig
}

// EthereumConfig 以太坊链配置
type EthereumConfig struct {
	RPCEndpoint string `json:"rpc_endpoint"`
	ChainID     int64  `json:"chain_id"`
	BatchSize   int    `json:"batch_size"`
	IsTestnet   bool   `json:"is_testnet"`
}

// NewEthereumProcessor 创建以太坊处理器
func NewEthereumProcessor(config *EthereumConfig) (*EthereumProcessor, error) {
	if config == nil {
		config = &EthereumConfig{
			RPCEndpoint: "https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
			ChainID:     1,
			BatchSize:   100,
			IsTestnet:   false,
		}
	}

	// 创建以太坊客户端
	client, err := ethclient.Dial(config.RPCEndpoint)
	if err != nil {
		return nil, fmt.Errorf("连接以太坊节点失败: %w", err)
	}

	// 确定链类型
	chainType := core.ChainTypeEthereum

	processor := &EthereumProcessor{
		client:    client,
		chainID:   big.NewInt(config.ChainID),
		chainType: chainType,
		config:    config,
	}

	// 测试连接 - 使用更长的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	log.Printf("🔍 测试以太坊连接: %s", config.RPCEndpoint)

	// 重试连接测试
	var lastErr error
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			log.Printf("🔄 重试连接测试 (%d/%d)", i+1, maxRetries)
			time.Sleep(time.Second * 2) // 等待2秒后重试
		}

		if err := processor.HealthCheck(ctx); err != nil {
			lastErr = err
			log.Printf("⚠️ 连接测试失败: %v", err)
			continue
		}

		// 连接成功
		lastErr = nil
		break
	}

	if lastErr != nil {
		return nil, fmt.Errorf("以太坊连接测试失败 (重试%d次后): %w", maxRetries, lastErr)
	}

	log.Printf("✅ %s处理器初始化成功: %s (链ID: %d)", chainType, config.RPCEndpoint, config.ChainID)
	return processor, nil
}

// GetChainType 获取链类型
func (e *EthereumProcessor) GetChainType() core.ChainType {
	return e.chainType
}

// GetChainID 获取链ID
func (e *EthereumProcessor) GetChainID() string {
	return e.chainID.String()
}

// GetLatestBlockNumber 获取最新区块号
func (e *EthereumProcessor) GetLatestBlockNumber(ctx context.Context) (*big.Int, error) {
	blockNumber, err := e.client.BlockNumber(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取最新区块号失败: %w", err)
	}

	return big.NewInt(int64(blockNumber)), nil
}

// GetTransactionsByBlockRange 批量获取交易 (通过区块范围)
func (e *EthereumProcessor) GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]core.UnifiedTransaction, error) {
	var allTransactions []core.UnifiedTransaction

	// 遍历区块范围
	for blockNum := new(big.Int).Set(startBlock); blockNum.Cmp(endBlock) <= 0; blockNum.Add(blockNum, big.NewInt(1)) {
		// 获取区块数据
		block, err := e.client.BlockByNumber(ctx, blockNum)
		if err != nil {
			log.Printf("⚠️ 获取区块 %s 失败: %v", blockNum.String(), err)
			continue
		}

		// 获取区块中的所有交易收据
		transactions, err := e.getTransactionsFromBlock(ctx, block)
		if err != nil {
			log.Printf("⚠️ 获取区块 %s 交易失败: %v", blockNum.String(), err)
			continue
		}

		allTransactions = append(allTransactions, transactions...)
	}

	return allTransactions, nil
}

// GetTransaction 获取单个交易
func (e *EthereumProcessor) GetTransaction(ctx context.Context, txHash string) (*core.UnifiedTransaction, error) {
	hash := common.HexToHash(txHash)

	// 获取交易信息
	tx, isPending, err := e.client.TransactionByHash(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取交易失败: %w", err)
	}

	if isPending {
		return e.convertPendingTransaction(tx), nil
	}

	// 获取交易收据
	receipt, err := e.client.TransactionReceipt(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取交易收据失败: %w", err)
	}

	// 获取区块信息
	block, err := e.client.BlockByHash(ctx, receipt.BlockHash)
	if err != nil {
		return nil, fmt.Errorf("获取区块信息失败: %w", err)
	}

	// 转换为统一交易格式
	unifiedTx, err := e.convertToUnifiedTransaction(tx, receipt, block)
	if err != nil {
		return nil, fmt.Errorf("转换交易格式失败: %w", err)
	}

	return unifiedTx, nil
}

// HealthCheck 健康检查
func (e *EthereumProcessor) HealthCheck(ctx context.Context) error {
	_, err := e.client.BlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("%s健康检查失败: %w", e.chainType, err)
	}
	return nil
}

// getTransactionsFromBlock 从区块中获取所有交易
func (e *EthereumProcessor) getTransactionsFromBlock(ctx context.Context, block *types.Block) ([]core.UnifiedTransaction, error) {
	var transactions []core.UnifiedTransaction

	blockTxCount := len(block.Transactions())
	log.Printf("🔍 处理区块 %s，包含 %d 个交易", block.Number().String(), blockTxCount)

	// 如果交易数量太多，限制处理数量以避免超时
	maxTxPerBlock := 100
	if blockTxCount > maxTxPerBlock {
		log.Printf("⚠️ 区块交易数量过多 (%d)，限制处理前 %d 个交易", blockTxCount, maxTxPerBlock)
		blockTxCount = maxTxPerBlock
	}

	// 批量获取交易收据 - 使用并发处理
	receipts := make([]*types.Receipt, blockTxCount)

	// 并发获取收据
	type receiptResult struct {
		index   int
		receipt *types.Receipt
		err     error
	}

	resultChan := make(chan receiptResult, blockTxCount)
	semaphore := make(chan struct{}, 10) // 限制并发数为10

	// 启动goroutines获取收据
	for i := 0; i < blockTxCount; i++ {
		go func(index int, tx *types.Transaction) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 为每个请求设置超时
			receiptCtx, cancel := context.WithTimeout(ctx, time.Second*30)
			defer cancel()

			receipt, err := e.client.TransactionReceipt(receiptCtx, tx.Hash())
			resultChan <- receiptResult{
				index:   index,
				receipt: receipt,
				err:     err,
			}
		}(i, block.Transactions()[i])
	}

	// 收集结果
	for i := 0; i < blockTxCount; i++ {
		result := <-resultChan
		if result.err != nil {
			log.Printf("⚠️ 获取交易收据失败 (index: %d): %v", result.index, result.err)
			continue
		}
		receipts[result.index] = result.receipt
	}

	// 转换交易 - 只处理我们实际获取的交易数量
	for i := 0; i < blockTxCount; i++ {
		if receipts[i] == nil {
			continue
		}

		tx := block.Transactions()[i]
		unifiedTx, err := e.convertToUnifiedTransaction(tx, receipts[i], block)
		if err != nil {
			log.Printf("⚠️ 转换交易失败 (hash: %s): %v", tx.Hash().Hex(), err)
			continue
		}

		transactions = append(transactions, *unifiedTx)
	}

	log.Printf("✅ 区块 %s 处理完成，成功转换 %d 个交易", block.Number().String(), len(transactions))

	return transactions, nil
}

// convertToUnifiedTransaction 转换为统一交易格式
func (e *EthereumProcessor) convertToUnifiedTransaction(tx *types.Transaction, receipt *types.Receipt, block *types.Block) (*core.UnifiedTransaction, error) {
	// 基础信息
	unifiedTx := &core.UnifiedTransaction{
		TxHash:      tx.Hash().Hex(),
		ChainType:   e.chainType,
		ChainID:     e.GetChainID(),
		BlockNumber: receipt.BlockNumber,
		BlockHash:   receipt.BlockHash.Hex(),
		TxIndex:     int(receipt.TransactionIndex),
		GasLimit:    big.NewInt(int64(tx.Gas())),
		GasUsed:     big.NewInt(int64(receipt.GasUsed)),
		GasPrice:    tx.GasPrice(),
		Value:       tx.Value(),
		Timestamp:   time.Unix(int64(block.Time()), 0),
		RawData: map[string]interface{}{
			"transaction": tx,
			"receipt":     receipt,
			"block":       block,
		},
	}

	// 发送者地址 - 支持多种交易类型
	var from common.Address
	var err error
	signers := []types.Signer{
		types.LatestSignerForChainID(e.chainID),
		types.NewEIP155Signer(e.chainID),
		types.NewLondonSigner(e.chainID),
		types.HomesteadSigner{},
	}

	for _, signer := range signers {
		from, err = types.Sender(signer, tx)
		if err == nil {
			break
		}
	}

	if err != nil {
		log.Printf("⚠️ 无法获取发送者地址 (hash: %s): %v", tx.Hash().Hex(), err)
		from = common.Address{}
	}
	unifiedTx.FromAddress = utils.NormalizeAddress(from.Hex())

	// 接收者地址
	if tx.To() != nil {
		unifiedTx.ToAddress = utils.NormalizeAddress(tx.To().Hex())
	}

	// 交易状态
	if receipt.Status == types.ReceiptStatusSuccessful {
		unifiedTx.Status = core.TransactionStatusSuccess
	} else {
		unifiedTx.Status = core.TransactionStatusFailed
	}

	return unifiedTx, nil
}

// convertPendingTransaction 转换待处理交易
func (e *EthereumProcessor) convertPendingTransaction(tx *types.Transaction) *core.UnifiedTransaction {
	unifiedTx := &core.UnifiedTransaction{
		TxHash:    tx.Hash().Hex(),
		ChainType: e.chainType,
		ChainID:   e.GetChainID(),
		GasLimit:  big.NewInt(int64(tx.Gas())),
		GasPrice:  tx.GasPrice(),
		Value:     tx.Value(),
		Status:    core.TransactionStatusPending,
		Timestamp: time.Now(),
		RawData:   tx,
	}

	// 尝试获取发送者地址
	if from, err := types.Sender(types.LatestSignerForChainID(e.chainID), tx); err == nil {
		unifiedTx.FromAddress = utils.NormalizeAddress(from.Hex())
	}

	// 接收者地址
	if tx.To() != nil {
		unifiedTx.ToAddress = utils.NormalizeAddress(tx.To().Hex())
	}

	return unifiedTx
}
