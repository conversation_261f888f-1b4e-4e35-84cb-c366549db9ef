package bsc

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"
	"time"

	"unified-tx-parser/pkg/core"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

// BSCProcessor BSC链处理器
type BSCProcessor struct {
	client  *ethclient.Client
	config  *BSCConfig
	chainID *big.Int
}

// BSCConfig BSC配置
type BSCConfig struct {
	RPCEndpoint string
	ChainID     int64
	BatchSize   int
}

// NewBSCProcessor 创建BSC处理器
func NewBSCProcessor(config *BSCConfig) (*BSCProcessor, error) {
	// 连接到BSC节点
	client, err := ethclient.Dial(config.RPCEndpoint)
	if err != nil {
		return nil, fmt.Errorf("连接BSC节点失败: %w", err)
	}

	processor := &BSCProcessor{
		client:  client,
		config:  config,
		chainID: big.NewInt(config.ChainID),
	}

	// 测试连接 - 使用更长的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()

	log.Printf("🔍 测试BSC连接: %s", config.RPCEndpoint)

	// 重试连接测试
	var lastErr error
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			log.Printf("🔄 重试连接测试 (%d/%d)", i+1, maxRetries)
			time.Sleep(time.Second * 2)
		}

		if err := processor.HealthCheck(ctx); err != nil {
			lastErr = err
			log.Printf("⚠️ 连接测试失败: %v", err)
			continue
		}

		lastErr = nil
		break
	}

	if lastErr != nil {
		return nil, fmt.Errorf("BSC连接测试失败 (重试%d次后): %w", maxRetries, lastErr)
	}

	log.Printf("✅ BSC处理器创建成功")
	return processor, nil
}

// GetChainType 获取链类型
func (b *BSCProcessor) GetChainType() core.ChainType {
	return core.ChainTypeBSC
}

// GetChainID 获取链ID
func (b *BSCProcessor) GetChainID() string {
	return fmt.Sprintf("%d", b.config.ChainID)
}

// HealthCheck 健康检查
func (b *BSCProcessor) HealthCheck(ctx context.Context) error {
	_, err := b.client.BlockNumber(ctx)
	if err != nil {
		return fmt.Errorf("bsc健康检查失败: %w", err)
	}
	return nil
}

// GetLatestBlockNumber 获取最新区块号
func (b *BSCProcessor) GetLatestBlockNumber(ctx context.Context) (*big.Int, error) {
	blockNumber, err := b.client.BlockNumber(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取BSC最新区块号失败: %w", err)
	}
	return big.NewInt(int64(blockNumber)), nil
}

// GetTransaction 获取单个交易
func (b *BSCProcessor) GetTransaction(ctx context.Context, txHash string) (*core.UnifiedTransaction, error) {
	hash := common.HexToHash(txHash)

	// 获取交易
	tx, isPending, err := b.client.TransactionByHash(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取BSC交易失败: %w", err)
	}

	if isPending {
		return nil, fmt.Errorf("交易仍在pending状态")
	}

	// 获取交易收据
	receipt, err := b.client.TransactionReceipt(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("获取BSC交易收据失败: %w", err)
	}

	// 获取区块信息
	block, err := b.client.BlockByHash(ctx, receipt.BlockHash)
	if err != nil {
		return nil, fmt.Errorf("获取BSC区块失败: %w", err)
	}

	return b.convertToUnifiedTransaction(tx, receipt, block)
}

// GetTransactionsByBlockRange 获取区块范围内的交易
func (b *BSCProcessor) GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]core.UnifiedTransaction, error) {
	var allTransactions []core.UnifiedTransaction

	for blockNum := new(big.Int).Set(startBlock); blockNum.Cmp(endBlock) <= 0; blockNum.Add(blockNum, big.NewInt(1)) {
		// 获取区块 - 添加重试机制
		block, err := b.getBlockWithRetry(ctx, blockNum, 3)
		if err != nil {
			log.Printf("⚠️ 跳过无法获取的区块 %s: %v", blockNum.String(), err)
			continue // 跳过有问题的区块，继续处理下一个
		}

		// 获取区块中的所有交易
		transactions, err := b.getTransactionsFromBlock(ctx, block)
		if err != nil {
			log.Printf("⚠️ 跳过区块 %s 中的交易处理: %v", blockNum.String(), err)
			continue // 跳过有问题的区块，继续处理下一个
		}

		allTransactions = append(allTransactions, transactions...)
	}

	return allTransactions, nil
}

// getBlockWithRetry 带重试机制的区块获取
func (b *BSCProcessor) getBlockWithRetry(ctx context.Context, blockNum *big.Int, maxRetries int) (*types.Block, error) {
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			log.Printf("🔄 重试获取BSC区块 %s (%d/%d)", blockNum.String(), i+1, maxRetries)
			time.Sleep(time.Second * time.Duration(i)) // 递增延迟
		}

		// 为每次尝试设置超时
		blockCtx, cancel := context.WithTimeout(ctx, time.Second*30)

		block, err := b.client.BlockByNumber(blockCtx, blockNum)
		cancel()

		if err == nil {
			return block, nil
		}

		lastErr = err

		// 如果是交易类型不支持的错误，尝试其他方法
		if strings.Contains(err.Error(), "transaction type not supported") {
			log.Printf("⚠️ 区块 %s 包含不支持的交易类型，尝试替代方法", blockNum.String())

			// 尝试只获取区块头信息
			header, headerErr := b.client.HeaderByNumber(blockCtx, blockNum)
			if headerErr == nil {
				log.Printf("✅ 成功获取区块 %s 的头信息，跳过交易处理", blockNum.String())
				// 创建一个空的区块对象，只包含头信息
				emptyBlock := types.NewBlockWithHeader(header)
				return emptyBlock, nil
			}
		}
	}

	return nil, fmt.Errorf("获取BSC区块 %s 失败 (重试%d次后): %w", blockNum.String(), maxRetries, lastErr)
}

// getTransactionsFromBlock 从区块中获取所有交易
func (b *BSCProcessor) getTransactionsFromBlock(ctx context.Context, block *types.Block) ([]core.UnifiedTransaction, error) {
	var transactions []core.UnifiedTransaction

	blockTxCount := len(block.Transactions())

	// 如果区块没有交易，直接返回
	if blockTxCount == 0 {
		log.Printf("📭 BSC区块 %s 没有交易", block.Number().String())
		return transactions, nil
	}

	log.Printf("🔍 处理BSC区块 %s，包含 %d 个交易", block.Number().String(), blockTxCount)

	// 如果交易数量太多，限制处理数量以避免超时
	maxTxPerBlock := 200 // BSC比以太坊快，可以处理更多交易
	if blockTxCount > maxTxPerBlock {
		log.Printf("⚠️ 区块交易数量过多 (%d)，限制处理前 %d 个交易", blockTxCount, maxTxPerBlock)
		blockTxCount = maxTxPerBlock
	}

	// 批量获取交易收据 - 使用并发处理
	receipts := make([]*types.Receipt, blockTxCount)

	// 并发获取收据
	type receiptResult struct {
		index   int
		receipt *types.Receipt
		err     error
	}

	resultChan := make(chan receiptResult, blockTxCount)
	semaphore := make(chan struct{}, 15) // BSC可以使用更多并发

	// 启动goroutines获取收据
	for i := 0; i < blockTxCount; i++ {
		go func(index int, tx *types.Transaction) {
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 为每个请求设置超时
			receiptCtx, cancel := context.WithTimeout(ctx, time.Second*20) // BSC响应更快
			defer cancel()

			receipt, err := b.client.TransactionReceipt(receiptCtx, tx.Hash())
			resultChan <- receiptResult{
				index:   index,
				receipt: receipt,
				err:     err,
			}
		}(i, block.Transactions()[i])
	}

	// 收集结果
	for i := 0; i < blockTxCount; i++ {
		result := <-resultChan
		if result.err != nil {
			log.Printf("⚠️ 获取BSC交易收据失败 (index: %d): %v", result.index, result.err)
			continue
		}
		receipts[result.index] = result.receipt
	}

	// 转换交易
	successCount := 0
	for i := 0; i < blockTxCount; i++ {
		if receipts[i] == nil {
			continue
		}

		tx := block.Transactions()[i]
		unifiedTx, err := b.convertToUnifiedTransaction(tx, receipts[i], block)
		if err != nil {
			log.Printf("⚠️ 转换BSC交易失败 (hash: %s): %v", tx.Hash().Hex(), err)
			continue
		}

		transactions = append(transactions, *unifiedTx)
		successCount++
	}

	if successCount == blockTxCount {
		log.Printf("✅ BSC区块 %s 处理完成，成功转换 %d 个交易", block.Number().String(), successCount)
	} else {
		log.Printf("⚠️ BSC区块 %s 处理完成，成功转换 %d/%d 个交易 (失败 %d 个)",
			block.Number().String(), successCount, blockTxCount, blockTxCount-successCount)
	}
	return transactions, nil
}

// convertToUnifiedTransaction 转换为统一交易格式
func (b *BSCProcessor) convertToUnifiedTransaction(tx *types.Transaction, receipt *types.Receipt, block *types.Block) (*core.UnifiedTransaction, error) {
	// 确定发送者地址 - 支持多种交易类型
	var from common.Address
	var err error

	// 尝试不同的签名器类型
	signers := []types.Signer{
		types.LatestSignerForChainID(b.chainID), // 支持最新的交易类型
		types.NewEIP155Signer(b.chainID),        // EIP-155
		types.NewLondonSigner(b.chainID),        // EIP-1559 (London fork)
		types.HomesteadSigner{},                 // Homestead
	}

	for _, signer := range signers {
		from, err = types.Sender(signer, tx)
		if err == nil {
			break
		}
	}

	if err != nil {
		// 如果所有签名器都失败，尝试从收据中获取
		log.Printf("⚠️ 无法通过签名器获取发送者地址，尝试其他方法: %v", err)
		// 作为最后手段，使用零地址
		from = common.Address{}
	}

	// 确定接收者地址
	var toAddress string
	if tx.To() != nil {
		toAddress = tx.To().Hex()
	} else {
		// 合约创建交易
		toAddress = receipt.ContractAddress.Hex()
	}

	// 确定交易状态
	var status core.TransactionStatus
	if receipt.Status == types.ReceiptStatusSuccessful {
		status = core.TransactionStatusSuccess
	} else {
		status = core.TransactionStatusFailed
	}

	// 处理Gas价格 - 支持不同的交易类型
	var gasPrice *big.Int
	if tx.GasPrice() != nil {
		gasPrice = tx.GasPrice()
	} else {
		// 对于EIP-1559交易，使用有效Gas价格
		if tx.GasFeeCap() != nil && tx.GasTipCap() != nil {
			// 简化计算：使用GasFeeCap作为Gas价格
			gasPrice = tx.GasFeeCap()
		} else {
			gasPrice = big.NewInt(0)
		}
	}

	unifiedTx := &core.UnifiedTransaction{
		ChainType:   core.ChainTypeBSC,
		ChainID:     fmt.Sprintf("%d", b.config.ChainID),
		TxHash:      tx.Hash().Hex(),
		BlockNumber: receipt.BlockNumber,
		BlockHash:   receipt.BlockHash.Hex(),
		TxIndex:     int(receipt.TransactionIndex),
		FromAddress: from.Hex(),
		ToAddress:   toAddress,
		Value:       tx.Value(),
		GasLimit:    big.NewInt(int64(tx.Gas())),
		GasUsed:     big.NewInt(int64(receipt.GasUsed)),
		GasPrice:    gasPrice,
		Status:      status,
		Timestamp:   time.Unix(int64(block.Time()), 0),
		RawData: map[string]interface{}{
			"transaction": tx,
			"receipt":     receipt,
			"block":       block,
		},
	}

	return unifiedTx, nil
}
