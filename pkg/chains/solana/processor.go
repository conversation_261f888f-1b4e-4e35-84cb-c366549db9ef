package solana

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"time"

	"unified-tx-parser/pkg/core"

	"github.com/gagliardetto/solana-go/rpc"
)

// SolanaProcessor Solana链处理器
type SolanaProcessor struct {
	client  *rpc.Client
	chainID string
	config  *SolanaConfig
}

// SolanaConfig Solana配置
type SolanaConfig struct {
	RPCEndpoint string `json:"rpc_endpoint"`
	ChainID     string `json:"chain_id"`
	BatchSize   int    `json:"batch_size"`
	IsTestnet   bool   `json:"is_testnet"`
}

// NewSolanaProcessor 创建新的Solana处理器
func NewSolanaProcessor(config *SolanaConfig) (*SolanaProcessor, error) {
	if config.BatchSize <= 0 {
		config.BatchSize = 100
	}

	// 创建Solana客户端
	client := rpc.New(config.RPCEndpoint)

	processor := &SolanaProcessor{
		client:  client,
		chainID: config.ChainID,
		config:  config,
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	if err := processor.HealthCheck(ctx); err != nil {
		return nil, fmt.Errorf("Solana连接测试失败: %w", err)
	}

	log.Printf("✅ Solana处理器初始化成功: %s", config.RPCEndpoint)
	return processor, nil
}

// GetChainType 获取链类型
func (s *SolanaProcessor) GetChainType() core.ChainType {
	return core.ChainTypeSolana
}

// GetChainID 获取链ID
func (s *SolanaProcessor) GetChainID() string {
	return s.chainID
}

// GetLatestBlockNumber 获取最新区块号 (Solana中是slot)
func (s *SolanaProcessor) GetLatestBlockNumber(ctx context.Context) (*big.Int, error) {
	slot, err := s.client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		return nil, fmt.Errorf("获取最新slot失败: %w", err)
	}

	return big.NewInt(int64(slot)), nil
}

// GetTransactionsByBlockRange 批量获取交易 (通过slot范围)
func (s *SolanaProcessor) GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]core.UnifiedTransaction, error) {
	var allTransactions []core.UnifiedTransaction

	// 遍历slot范围
	for slot := startBlock.Uint64(); slot <= endBlock.Uint64(); slot++ {
		// 获取区块数据
		block, err := s.client.GetBlock(ctx, slot)
		if err != nil {
			log.Printf("⚠️ 获取区块 %d 失败: %v", slot, err)
			continue
		}

		if block == nil || len(block.Transactions) == 0 {
			continue
		}

		// 处理区块中的交易
		for i, tx := range block.Transactions {
			unifiedTx := &core.UnifiedTransaction{
				TxHash:      fmt.Sprintf("tx_%d_%d", slot, i), // 临时哈希
				ChainType:   core.ChainTypeSolana,
				ChainID:     s.chainID,
				BlockNumber: big.NewInt(int64(slot)),
				Status:      core.TransactionStatusSuccess,
				Timestamp:   time.Now(),
				RawData:     tx,
			}

			// 设置交易状态
			if tx.Meta != nil && tx.Meta.Err != nil {
				unifiedTx.Status = core.TransactionStatusFailed
			}

			// 设置gas费用
			if tx.Meta != nil {
				unifiedTx.GasUsed = big.NewInt(int64(tx.Meta.Fee))
			}

			allTransactions = append(allTransactions, *unifiedTx)
		}
	}

	return allTransactions, nil
}

// GetTransaction 获取单个交易 - 简化实现
func (s *SolanaProcessor) GetTransaction(ctx context.Context, txHash string) (*core.UnifiedTransaction, error) {
	// Solana交易查询比较复杂，这里返回一个基础实现
	unifiedTx := &core.UnifiedTransaction{
		TxHash:    txHash,
		ChainType: core.ChainTypeSolana,
		ChainID:   s.chainID,
		Status:    core.TransactionStatusSuccess,
		Timestamp: time.Now(),
	}

	return unifiedTx, nil
}

// HealthCheck 健康检查
func (s *SolanaProcessor) HealthCheck(ctx context.Context) error {
	_, err := s.client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		return fmt.Errorf("Solana健康检查失败: %w", err)
	}
	return nil
}
