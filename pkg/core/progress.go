package core

import (
	"fmt"
	"math/big"
	"sync"
	"time"
)

// ProcessingStats 处理统计
type ProcessingStats struct {
	ChainType          ChainType     `json:"chain_type"`           // 链类型
	ProcessingDuration time.Duration `json:"processing_duration"`  // 处理持续时间
	BlocksProcessed    int64         `json:"blocks_processed"`     // 已处理区块数
	TransactionsPerSec float64       `json:"transactions_per_sec"` // 每秒交易数
	EventsPerSec       float64       `json:"events_per_sec"`       // 每秒事件数
	AverageBlockTime   time.Duration `json:"average_block_time"`   // 平均区块时间
	LastProcessingTime time.Duration `json:"last_processing_time"` // 最后处理耗时
	ErrorRate          float64       `json:"error_rate"`           // 错误率
	RetryCount         int64         `json:"retry_count"`          // 重试次数
}

// GlobalProcessingStats 全局处理统计
type GlobalProcessingStats struct {
	TotalChains        int                            `json:"total_chains"`         // 总链数
	ActiveChains       int                            `json:"active_chains"`        // 活跃链数
	TotalTransactions  int64                          `json:"total_transactions"`   // 总交易数
	TotalEvents        int64                          `json:"total_events"`         // 总事件数
	OverallSuccessRate float64                        `json:"overall_success_rate"` // 整体成功率
	ChainStats         map[ChainType]*ProcessingStats `json:"chain_stats"`          // 各链统计
	LastUpdateTime     time.Time                      `json:"last_update_time"`     // 最后更新时间
	SystemUptime       time.Duration                  `json:"system_uptime"`        // 系统运行时间
}

// ProcessingError 处理错误
type ProcessingError struct {
	ChainType   ChainType `json:"chain_type"`   // 链类型
	ErrorTime   time.Time `json:"error_time"`   // 错误时间
	ErrorType   string    `json:"error_type"`   // 错误类型
	ErrorMsg    string    `json:"error_msg"`    // 错误消息
	BlockNumber *big.Int  `json:"block_number"` // 出错的区块号
	TxHash      string    `json:"tx_hash"`      // 出错的交易哈希
	RetryCount  int       `json:"retry_count"`  // 重试次数
	Resolved    bool      `json:"resolved"`     // 是否已解决
}

// ProcessingMetrics 处理指标
type ProcessingMetrics struct {
	ChainType        ChainType     `json:"chain_type"`        // 链类型
	Timestamp        time.Time     `json:"timestamp"`         // 时间戳
	BlockNumber      *big.Int      `json:"block_number"`      // 区块号
	ProcessingTime   time.Duration `json:"processing_time"`   // 处理耗时
	TransactionCount int           `json:"transaction_count"` // 交易数量
	EventCount       int           `json:"event_count"`       // 事件数量
	MemoryUsage      int64         `json:"memory_usage"`      // 内存使用量
	CPUUsage         float64       `json:"cpu_usage"`         // CPU使用率
}

// PerformanceReport 性能报告
type PerformanceReport struct {
	ChainType          ChainType     `json:"chain_type"`           // 链类型
	ReportPeriod       time.Duration `json:"report_period"`        // 报告周期
	AverageProcessTime time.Duration `json:"average_process_time"` // 平均处理时间
	MaxProcessTime     time.Duration `json:"max_process_time"`     // 最大处理时间
	MinProcessTime     time.Duration `json:"min_process_time"`     // 最小处理时间
	TotalTransactions  int64         `json:"total_transactions"`   // 总交易数
	TotalEvents        int64         `json:"total_events"`         // 总事件数
	AverageMemoryUsage int64         `json:"average_memory_usage"` // 平均内存使用
	AverageCPUUsage    float64       `json:"average_cpu_usage"`    // 平均CPU使用率
	ThroughputTPS      float64       `json:"throughput_tps"`       // 吞吐量(TPS)
	ErrorCount         int64         `json:"error_count"`          // 错误数量
}

// MemoryProgressTracker 内存版本的进度跟踪器
type MemoryProgressTracker struct {
	mu                sync.RWMutex
	progresses        map[ChainType]*ProcessProgress
	stats             map[ChainType]*ProcessingStats
	errors            map[ChainType][]ProcessingError
	metrics           map[ChainType][]ProcessingMetrics
	globalStats       *GlobalProcessingStats
	systemStartTime   time.Time
	maxErrorHistory   int
	maxMetricsHistory int
}

// NewMemoryProgressTracker 创建内存进度跟踪器
func NewMemoryProgressTracker() *MemoryProgressTracker {
	return &MemoryProgressTracker{
		progresses:        make(map[ChainType]*ProcessProgress),
		stats:             make(map[ChainType]*ProcessingStats),
		errors:            make(map[ChainType][]ProcessingError),
		metrics:           make(map[ChainType][]ProcessingMetrics),
		systemStartTime:   time.Now(),
		maxErrorHistory:   1000,
		maxMetricsHistory: 10000,
		globalStats: &GlobalProcessingStats{
			ChainStats:     make(map[ChainType]*ProcessingStats),
			LastUpdateTime: time.Now(),
		},
	}
}

// GetProgress 获取处理进度
func (m *MemoryProgressTracker) GetProgress(chainType ChainType) (*ProcessProgress, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	progress, exists := m.progresses[chainType]
	if !exists {
		// 返回默认进度
		return &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
			ProcessingStatus:   ProcessingStatusIdle,
			StartTime:          time.Now(),
		}, nil
	}

	// 返回副本避免并发修改
	progressCopy := *progress
	return &progressCopy, nil
}

// UpdateProgress 更新进度
func (m *MemoryProgressTracker) UpdateProgress(chainType ChainType, progress *ProcessProgress) error {
	if progress == nil {
		return fmt.Errorf("进度信息不能为空")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 更新进度
	progress.LastUpdateTime = time.Now()
	progress.ChainType = chainType

	// 计算成功率
	if progress.TotalTransactions > 0 {
		successfulTx := progress.TotalTransactions - progress.ErrorCount
		progress.SuccessRate = float64(successfulTx) / float64(progress.TotalTransactions) * 100
	}

	m.progresses[chainType] = progress

	// 更新全局统计
	m.updateGlobalStats()

	return nil
}

// ResetProgress 重置进度
func (m *MemoryProgressTracker) ResetProgress(chainType ChainType) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 重置进度
	m.progresses[chainType] = &ProcessProgress{
		ChainType:          chainType,
		LastProcessedBlock: big.NewInt(0),
		LastUpdateTime:     time.Now(),
		ProcessingStatus:   ProcessingStatusIdle,
		StartTime:          time.Now(),
		TotalTransactions:  0,
		TotalEvents:        0,
		ErrorCount:         0,
		SuccessRate:        100.0,
	}

	// 清除相关数据
	delete(m.stats, chainType)
	delete(m.errors, chainType)
	delete(m.metrics, chainType)

	// 更新全局统计
	m.updateGlobalStats()

	return nil
}

// GetAllProgress 获取所有进度
func (m *MemoryProgressTracker) GetAllProgress() (map[ChainType]*ProcessProgress, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[ChainType]*ProcessProgress)
	for chainType, progress := range m.progresses {
		progressCopy := *progress
		result[chainType] = &progressCopy
	}

	return result, nil
}

// UpdateMultipleProgress 批量更新进度
func (m *MemoryProgressTracker) UpdateMultipleProgress(progresses map[ChainType]*ProcessProgress) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	for chainType, progress := range progresses {
		if progress == nil {
			continue
		}

		progress.LastUpdateTime = time.Now()
		progress.ChainType = chainType

		// 计算成功率
		if progress.TotalTransactions > 0 {
			successfulTx := progress.TotalTransactions - progress.ErrorCount
			progress.SuccessRate = float64(successfulTx) / float64(progress.TotalTransactions) * 100
		}

		m.progresses[chainType] = progress
	}

	// 更新全局统计
	m.updateGlobalStats()

	return nil
}

// updateGlobalStats 更新全局统计（内部方法，需要持有锁）
func (m *MemoryProgressTracker) updateGlobalStats() {
	m.globalStats.TotalChains = len(m.progresses)
	m.globalStats.ActiveChains = 0
	m.globalStats.TotalTransactions = 0
	m.globalStats.TotalEvents = 0
	m.globalStats.LastUpdateTime = time.Now()
	m.globalStats.SystemUptime = time.Since(m.systemStartTime)

	var totalSuccessRate float64
	activeChains := 0

	for chainType, progress := range m.progresses {
		m.globalStats.TotalTransactions += progress.TotalTransactions
		m.globalStats.TotalEvents += progress.TotalEvents

		if progress.ProcessingStatus == ProcessingStatusRunning ||
			progress.ProcessingStatus == ProcessingStatusSyncing ||
			progress.ProcessingStatus == ProcessingStatusCatchingUp {
			m.globalStats.ActiveChains++
		}

		if progress.TotalTransactions > 0 {
			totalSuccessRate += progress.SuccessRate
			activeChains++
		}

		// 更新链统计
		if stats, exists := m.stats[chainType]; exists {
			m.globalStats.ChainStats[chainType] = stats
		}
	}

	// 计算整体成功率
	if activeChains > 0 {
		m.globalStats.OverallSuccessRate = totalSuccessRate / float64(activeChains)
	}
}

// GetProcessingStats 获取处理统计
func (m *MemoryProgressTracker) GetProcessingStats(chainType ChainType) (*ProcessingStats, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats, exists := m.stats[chainType]
	if !exists {
		return &ProcessingStats{
			ChainType: chainType,
		}, nil
	}

	// 返回副本
	statsCopy := *stats
	return &statsCopy, nil
}

// GetGlobalStats 获取全局统计
func (m *MemoryProgressTracker) GetGlobalStats() (*GlobalProcessingStats, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 返回副本
	globalStatsCopy := *m.globalStats
	globalStatsCopy.ChainStats = make(map[ChainType]*ProcessingStats)
	for chainType, stats := range m.globalStats.ChainStats {
		statsCopy := *stats
		globalStatsCopy.ChainStats[chainType] = &statsCopy
	}

	return &globalStatsCopy, nil
}

// SetProcessingStatus 设置处理状态
func (m *MemoryProgressTracker) SetProcessingStatus(chainType ChainType, status ProcessingStatus) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	progress, exists := m.progresses[chainType]
	if !exists {
		// 创建新的进度记录
		progress = &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			StartTime:          time.Now(),
		}
		m.progresses[chainType] = progress
	}

	progress.ProcessingStatus = status
	progress.LastUpdateTime = time.Now()

	// 更新全局统计
	m.updateGlobalStats()

	return nil
}

// GetProcessingStatus 获取处理状态
func (m *MemoryProgressTracker) GetProcessingStatus(chainType ChainType) (ProcessingStatus, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	progress, exists := m.progresses[chainType]
	if !exists {
		return ProcessingStatusIdle, nil
	}

	return progress.ProcessingStatus, nil
}

// RecordError 记录错误
func (m *MemoryProgressTracker) RecordError(chainType ChainType, err error) error {
	if err == nil {
		return nil
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 创建错误记录
	processingError := ProcessingError{
		ChainType: chainType,
		ErrorTime: time.Now(),
		ErrorType: fmt.Sprintf("%T", err),
		ErrorMsg:  err.Error(),
		Resolved:  false,
	}

	// 添加到错误历史
	if _, exists := m.errors[chainType]; !exists {
		m.errors[chainType] = make([]ProcessingError, 0)
	}

	m.errors[chainType] = append(m.errors[chainType], processingError)

	// 限制错误历史长度
	if len(m.errors[chainType]) > m.maxErrorHistory {
		m.errors[chainType] = m.errors[chainType][len(m.errors[chainType])-m.maxErrorHistory:]
	}

	// 更新进度中的错误计数
	if progress, exists := m.progresses[chainType]; exists {
		progress.ErrorCount++
		progress.LastErrorTime = time.Now()

		// 重新计算成功率
		if progress.TotalTransactions > 0 {
			successfulTx := progress.TotalTransactions - progress.ErrorCount
			progress.SuccessRate = float64(successfulTx) / float64(progress.TotalTransactions) * 100
		}
	}

	return nil
}

// GetErrorHistory 获取错误历史
func (m *MemoryProgressTracker) GetErrorHistory(chainType ChainType, limit int) ([]ProcessingError, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	errors, exists := m.errors[chainType]
	if !exists {
		return []ProcessingError{}, nil
	}

	// 应用限制
	if limit > 0 && len(errors) > limit {
		errors = errors[len(errors)-limit:]
	}

	// 返回副本
	result := make([]ProcessingError, len(errors))
	copy(result, errors)

	return result, nil
}

// ClearErrorHistory 清除错误历史
func (m *MemoryProgressTracker) ClearErrorHistory(chainType ChainType) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.errors, chainType)

	// 重置进度中的错误计数
	if progress, exists := m.progresses[chainType]; exists {
		progress.ErrorCount = 0
		progress.LastErrorTime = time.Time{}

		// 重新计算成功率
		if progress.TotalTransactions > 0 {
			progress.SuccessRate = 100.0
		}
	}

	return nil
}

// RecordProcessingMetrics 记录处理指标
func (m *MemoryProgressTracker) RecordProcessingMetrics(chainType ChainType, metrics *ProcessingMetrics) error {
	if metrics == nil {
		return fmt.Errorf("指标信息不能为空")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 设置时间戳和链类型
	metrics.Timestamp = time.Now()
	metrics.ChainType = chainType

	// 添加到指标历史
	if _, exists := m.metrics[chainType]; !exists {
		m.metrics[chainType] = make([]ProcessingMetrics, 0)
	}

	m.metrics[chainType] = append(m.metrics[chainType], *metrics)

	// 限制指标历史长度
	if len(m.metrics[chainType]) > m.maxMetricsHistory {
		m.metrics[chainType] = m.metrics[chainType][len(m.metrics[chainType])-m.maxMetricsHistory:]
	}

	// 更新统计信息
	m.updateProcessingStats(chainType)

	return nil
}

// updateProcessingStats 更新处理统计（内部方法）
func (m *MemoryProgressTracker) updateProcessingStats(chainType ChainType) {
	metrics, exists := m.metrics[chainType]
	if !exists || len(metrics) == 0 {
		return
	}

	progress, progressExists := m.progresses[chainType]
	if !progressExists {
		return
	}

	// 计算统计信息
	stats := &ProcessingStats{
		ChainType: chainType,
	}

	if len(metrics) > 0 {
		// 计算处理持续时间
		if progress.StartTime.IsZero() {
			stats.ProcessingDuration = 0
		} else {
			stats.ProcessingDuration = time.Since(progress.StartTime)
		}

		// 计算平均处理时间
		var totalProcessingTime time.Duration
		var totalTransactions, totalEvents int64
		var totalMemory int64
		var totalCPU float64

		for _, metric := range metrics {
			totalProcessingTime += metric.ProcessingTime
			totalTransactions += int64(metric.TransactionCount)
			totalEvents += int64(metric.EventCount)
			totalMemory += metric.MemoryUsage
			totalCPU += metric.CPUUsage
		}

		metricsCount := int64(len(metrics))
		if metricsCount > 0 {
			stats.LastProcessingTime = totalProcessingTime / time.Duration(metricsCount)
		}

		// 计算TPS和EPS
		if stats.ProcessingDuration > 0 {
			stats.TransactionsPerSec = float64(progress.TotalTransactions) / stats.ProcessingDuration.Seconds()
			stats.EventsPerSec = float64(progress.TotalEvents) / stats.ProcessingDuration.Seconds()
		}

		// 计算错误率
		if progress.TotalTransactions > 0 {
			stats.ErrorRate = float64(progress.ErrorCount) / float64(progress.TotalTransactions) * 100
		}

		// 计算已处理区块数
		if progress.LastProcessedBlock != nil {
			stats.BlocksProcessed = progress.LastProcessedBlock.Int64()
		}
	}

	m.stats[chainType] = stats
}

// GetPerformanceMetrics 获取性能指标
func (m *MemoryProgressTracker) GetPerformanceMetrics(chainType ChainType, duration time.Duration) (*PerformanceReport, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	metrics, exists := m.metrics[chainType]
	if !exists || len(metrics) == 0 {
		return &PerformanceReport{
			ChainType:    chainType,
			ReportPeriod: duration,
		}, nil
	}

	// 过滤指定时间范围内的指标
	cutoffTime := time.Now().Add(-duration)
	var filteredMetrics []ProcessingMetrics

	for _, metric := range metrics {
		if metric.Timestamp.After(cutoffTime) {
			filteredMetrics = append(filteredMetrics, metric)
		}
	}

	if len(filteredMetrics) == 0 {
		return &PerformanceReport{
			ChainType:    chainType,
			ReportPeriod: duration,
		}, nil
	}

	// 计算性能报告
	report := &PerformanceReport{
		ChainType:    chainType,
		ReportPeriod: duration,
	}

	var totalProcessTime time.Duration
	var totalTransactions, totalEvents int64
	var totalMemory int64
	var totalCPU float64
	var maxProcessTime, minProcessTime time.Duration

	for i, metric := range filteredMetrics {
		totalProcessTime += metric.ProcessingTime
		totalTransactions += int64(metric.TransactionCount)
		totalEvents += int64(metric.EventCount)
		totalMemory += metric.MemoryUsage
		totalCPU += metric.CPUUsage

		if i == 0 {
			maxProcessTime = metric.ProcessingTime
			minProcessTime = metric.ProcessingTime
		} else {
			if metric.ProcessingTime > maxProcessTime {
				maxProcessTime = metric.ProcessingTime
			}
			if metric.ProcessingTime < minProcessTime {
				minProcessTime = metric.ProcessingTime
			}
		}
	}

	metricsCount := int64(len(filteredMetrics))
	if metricsCount > 0 {
		report.AverageProcessTime = totalProcessTime / time.Duration(metricsCount)
		report.MaxProcessTime = maxProcessTime
		report.MinProcessTime = minProcessTime
		report.TotalTransactions = totalTransactions
		report.TotalEvents = totalEvents
		report.AverageMemoryUsage = totalMemory / metricsCount
		report.AverageCPUUsage = totalCPU / float64(metricsCount)

		// 计算吞吐量
		if duration > 0 {
			report.ThroughputTPS = float64(totalTransactions) / duration.Seconds()
		}
	}

	// 获取错误数量
	if errors, exists := m.errors[chainType]; exists {
		for _, err := range errors {
			if err.ErrorTime.After(cutoffTime) {
				report.ErrorCount++
			}
		}
	}

	return report, nil
}

// HealthCheck 健康检查
func (m *MemoryProgressTracker) HealthCheck() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 检查基本状态
	if m.progresses == nil {
		return fmt.Errorf("进度跟踪器未初始化")
	}

	// 检查内存使用情况
	totalMetrics := 0
	totalErrors := 0

	for _, metrics := range m.metrics {
		totalMetrics += len(metrics)
	}

	for _, errors := range m.errors {
		totalErrors += len(errors)
	}

	// 如果内存使用过多，发出警告
	if totalMetrics > m.maxMetricsHistory*10 {
		return fmt.Errorf("指标历史过多，可能存在内存泄漏")
	}

	if totalErrors > m.maxErrorHistory*10 {
		return fmt.Errorf("错误历史过多，可能存在内存泄漏")
	}

	return nil
}

// Cleanup 清理旧数据
func (m *MemoryProgressTracker) Cleanup(olderThan time.Duration) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	cutoffTime := time.Now().Add(-olderThan)

	// 清理旧的指标数据
	for chainType, metrics := range m.metrics {
		var filteredMetrics []ProcessingMetrics
		for _, metric := range metrics {
			if metric.Timestamp.After(cutoffTime) {
				filteredMetrics = append(filteredMetrics, metric)
			}
		}
		m.metrics[chainType] = filteredMetrics
	}

	// 清理旧的错误数据
	for chainType, errors := range m.errors {
		var filteredErrors []ProcessingError
		for _, err := range errors {
			if err.ErrorTime.After(cutoffTime) {
				filteredErrors = append(filteredErrors, err)
			}
		}
		m.errors[chainType] = filteredErrors
	}

	return nil
}
