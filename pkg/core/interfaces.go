package core

import (
	"context"
	"math/big"
	"time"
	"unified-tx-parser/pkg/model"
)

// ChainType 链类型
type ChainType string

const (
	ChainTypeEthereum ChainType = "ethereum"
	ChainTypeBSC      ChainType = "bsc"
	ChainTypeSolana   ChainType = "solana"
	ChainTypeSui      ChainType = "sui"
)

// TransactionStatus 交易状态
type TransactionStatus string

const (
	TransactionStatusSuccess TransactionStatus = "success"
	TransactionStatusFailed  TransactionStatus = "failed"
	TransactionStatusPending TransactionStatus = "pending"
)

// DEXEventType DEX事件类型
type DEXEventType string

const (
	DEXEventTypeSwap            DEXEventType = "swap"
	DEXEventTypeAddLiquidity    DEXEventType = "add_liquidity"
	DEXEventTypeRemoveLiquidity DEXEventType = "remove_liquidity"
	DEXEventTypePoolCreated     DEXEventType = "pool_created"
	DEXEventTypeReserveUpdate   DEXEventType = "reserve_update"
)

// BlockData 区块数据结构
type BlockData struct {
	ChainType   ChainType `json:"chain_type"`
	BlockNumber *big.Int  `json:"block_number"`
	BlockHash   string    `json:"block_hash"`
	Timestamp   time.Time `json:"timestamp"`

	// 解析后的DEX数据
	Transactions []model.Transaction `json:"transactions"`
	Pools        []model.Pool        `json:"pools"`
	Tokens       []model.Token       `json:"tokens"`
	Liquidity    []model.Liquidity   `json:"liquidity"`
	Reserves     []model.Reserve     `json:"reserves"`

	// 原始数据
	RawData interface{} `json:"raw_data"`
}

// DEXEvent DEX事件
type DEXEvent struct {
	// 基础信息
	EventID   string       `json:"event_id"`
	EventType DEXEventType `json:"event_type"`
	Protocol  string       `json:"protocol"`

	// 关联交易
	TxHash    string    `json:"tx_hash"`
	ChainType ChainType `json:"chain_type"`

	// 事件数据
	Data interface{} `json:"data"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// SwapEventData 交换事件数据
type SwapEventData struct {
	PoolID    string   `json:"pool_id"`
	TokenIn   string   `json:"token_in"`
	TokenOut  string   `json:"token_out"`
	AmountIn  *big.Int `json:"amount_in"`
	AmountOut *big.Int `json:"amount_out"`
	Sender    string   `json:"sender"`
	Recipient string   `json:"recipient"`
	Price     string   `json:"price"`
	FeePaid   *big.Int `json:"fee_paid"`
}

// LiquidityEventData 流动性事件数据
type LiquidityEventData struct {
	PoolID          string   `json:"pool_id"`
	TokenA          string   `json:"token_a"`
	TokenB          string   `json:"token_b"`
	AmountA         *big.Int `json:"amount_a"`
	AmountB         *big.Int `json:"amount_b"`
	LiquidityMinted *big.Int `json:"liquidity_minted,omitempty"`
	LiquidityBurned *big.Int `json:"liquidity_burned,omitempty"`
	Provider        string   `json:"provider"`
}

// ChainDEXProcessor 链和DEX处理器接口（合并后的统一接口）
type ChainDEXProcessor interface {
	// 获取链信息
	GetChainType() ChainType
	GetChainID() string

	// 获取最新块信息
	GetLatestBlockNumber(ctx context.Context) (*big.Int, error)

	// 批量处理区块，直接返回解析后的DEX数据
	ProcessBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]BlockData, error)

	// 处理单个区块
	ProcessBlock(ctx context.Context, blockNumber *big.Int) (*BlockData, error)

	// 获取支持的DEX协议
	GetSupportedProtocols() []string

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// StorageEngine 存储引擎接口
type StorageEngine interface {
	// 批量存储区块数据（包含5张表的数据）
	StoreBlockData(ctx context.Context, blockData []BlockData) error

	// 分别存储各类数据
	StoreTransactions(ctx context.Context, transactions []model.Transaction) error
	StorePools(ctx context.Context, pools []model.Pool) error
	StoreTokens(ctx context.Context, tokens []model.Token) error
	StoreLiquidity(ctx context.Context, liquidity []model.Liquidity) error
	StoreReserves(ctx context.Context, reserves []model.Reserve) error

	// 查询功能
	GetTransactionsByHash(ctx context.Context, hashes []string) ([]model.Transaction, error)
	GetPoolsByAddress(ctx context.Context, addresses []string) ([]model.Pool, error)
	GetTokensByAddress(ctx context.Context, addresses []string) ([]model.Token, error)
	GetLiquidityByPool(ctx context.Context, poolAddress string, limit int) ([]model.Liquidity, error)
	GetReservesByPool(ctx context.Context, poolAddress string, limit int) ([]model.Reserve, error)

	// 统计功能
	GetStorageStats(ctx context.Context) (map[string]interface{}, error)

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// ProgressTracker 进度跟踪器接口
type ProgressTracker interface {
	// 基础进度管理
	GetProgress(chainType ChainType) (*ProcessProgress, error)
	UpdateProgress(chainType ChainType, progress *ProcessProgress) error
	ResetProgress(chainType ChainType) error

	// 批量操作
	GetAllProgress() (map[ChainType]*ProcessProgress, error)
	UpdateMultipleProgress(progresses map[ChainType]*ProcessProgress) error

	// 统计信息
	GetProcessingStats(chainType ChainType) (*ProcessingStats, error)
	GetGlobalStats() (*GlobalProcessingStats, error)

	// 状态管理
	SetProcessingStatus(chainType ChainType, status ProcessingStatus) error
	GetProcessingStatus(chainType ChainType) (ProcessingStatus, error)

	// 错误跟踪
	RecordError(chainType ChainType, err error) error
	GetErrorHistory(chainType ChainType, limit int) ([]ProcessingError, error)
	ClearErrorHistory(chainType ChainType) error

	// 性能监控
	RecordProcessingMetrics(chainType ChainType, metrics *ProcessingMetrics) error
	GetPerformanceMetrics(chainType ChainType, duration time.Duration) (*PerformanceReport, error)

	// 健康检查和维护
	HealthCheck() error
	Cleanup(olderThan time.Duration) error
}

// ProcessProgress 处理进度
type ProcessProgress struct {
	ChainType          ChainType        `json:"chain_type"`           // 链类型
	LastProcessedBlock *big.Int         `json:"last_processed_block"` // 最后处理的区块号
	LastUpdateTime     time.Time        `json:"last_update_time"`     // 最后更新时间
	TotalTransactions  int64            `json:"total_transactions"`   // 总交易数
	TotalEvents        int64            `json:"total_events"`         // 总事件数
	ProcessingStatus   ProcessingStatus `json:"processing_status"`    // 处理状态
	StartTime          time.Time        `json:"start_time"`           // 开始处理时间
	LastErrorTime      time.Time        `json:"last_error_time"`      // 最后错误时间
	ErrorCount         int64            `json:"error_count"`          // 错误计数
	SuccessRate        float64          `json:"success_rate"`         // 成功率
}

// ProcessingStatus 处理状态
type ProcessingStatus string

const (
	ProcessingStatusIdle       ProcessingStatus = "idle"        // 空闲
	ProcessingStatusRunning    ProcessingStatus = "running"     // 运行中
	ProcessingStatusPaused     ProcessingStatus = "paused"      // 暂停
	ProcessingStatusError      ProcessingStatus = "error"       // 错误
	ProcessingStatusStopped    ProcessingStatus = "stopped"     // 已停止
	ProcessingStatusSyncing    ProcessingStatus = "syncing"     // 同步中
	ProcessingStatusCatchingUp ProcessingStatus = "catching_up" // 追赶中
)
