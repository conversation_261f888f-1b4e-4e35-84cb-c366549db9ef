package core

import (
	"context"
	"math/big"
	"time"
)

// ChainType 链类型
type ChainType string

const (
	ChainTypeEthereum ChainType = "ethereum"
	ChainTypeBSC      ChainType = "bsc"
	ChainTypeSolana   ChainType = "solana"
	ChainTypeSui      ChainType = "sui"
)

// TransactionStatus 交易状态
type TransactionStatus string

const (
	TransactionStatusSuccess TransactionStatus = "success"
	TransactionStatusFailed  TransactionStatus = "failed"
	TransactionStatusPending TransactionStatus = "pending"
)

// BusinessEventType 业务事件类型
type BusinessEventType string

const (
	EventTypeSwap            BusinessEventType = "swap"
	EventTypeAddLiquidity    BusinessEventType = "add_liquidity"
	EventTypeRemoveLiquidity BusinessEventType = "remove_liquidity"
	EventTypePoolCreated     BusinessEventType = "pool_created"
	EventTypeStake           BusinessEventType = "stake"
	EventTypeUnstake         BusinessEventType = "unstake"
	EventTypeTransfer        BusinessEventType = "transfer"
	EventTypeBridge          BusinessEventType = "bridge"
)

// UnifiedTransaction 统一交易结构
type UnifiedTransaction struct {
	// 基础信息
	TxHash    string    `json:"tx_hash"`
	ChainType ChainType `json:"chain_type"`
	ChainID   string    `json:"chain_id"`

	// 交易信息
	BlockNumber *big.Int          `json:"block_number"`
	BlockHash   string            `json:"block_hash"`
	TxIndex     int               `json:"tx_index"`
	FromAddress string            `json:"from_address"`
	ToAddress   string            `json:"to_address"`
	Value       *big.Int          `json:"value"`
	GasLimit    *big.Int          `json:"gas_limit"`
	GasUsed     *big.Int          `json:"gas_used"`
	GasPrice    *big.Int          `json:"gas_price"`
	Status      TransactionStatus `json:"status"`

	// 时间信息
	Timestamp time.Time `json:"timestamp"`

	// 原始数据
	RawData interface{} `json:"raw_data"`

	// 业务事件（由提取器提取）
	BusinessEvents []BusinessEvent `json:"business_events"`
}

// BusinessEvent 业务事件
type BusinessEvent struct {
	// 基础信息
	EventID   string            `json:"event_id"`
	EventType BusinessEventType `json:"event_type"`
	Protocol  string            `json:"protocol"`

	// 关联交易
	TxHash    string    `json:"tx_hash"`
	ChainType ChainType `json:"chain_type"`

	// 事件数据
	Data interface{} `json:"data"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// SwapEventData 交换事件数据
type SwapEventData struct {
	PoolID    string   `json:"pool_id"`
	TokenIn   string   `json:"token_in"`
	TokenOut  string   `json:"token_out"`
	AmountIn  *big.Int `json:"amount_in"`
	AmountOut *big.Int `json:"amount_out"`
	Sender    string   `json:"sender"`
	Recipient string   `json:"recipient"`
	Price     string   `json:"price"`
	FeePaid   *big.Int `json:"fee_paid"`
}

// LiquidityEventData 流动性事件数据
type LiquidityEventData struct {
	PoolID          string   `json:"pool_id"`
	TokenA          string   `json:"token_a"`
	TokenB          string   `json:"token_b"`
	AmountA         *big.Int `json:"amount_a"`
	AmountB         *big.Int `json:"amount_b"`
	LiquidityMinted *big.Int `json:"liquidity_minted,omitempty"`
	LiquidityBurned *big.Int `json:"liquidity_burned,omitempty"`
	Provider        string   `json:"provider"`
}

// ChainProcessor 链处理器接口
type ChainProcessor interface {
	// 获取链信息
	GetChainType() ChainType
	GetChainID() string

	// 获取最新块信息
	GetLatestBlockNumber(ctx context.Context) (*big.Int, error)

	// 批量获取交易
	GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]UnifiedTransaction, error)

	// 获取单个交易
	GetTransaction(ctx context.Context, txHash string) (*UnifiedTransaction, error)

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// BusinessEventExtractor 业务事件提取器接口
type BusinessEventExtractor interface {
	// 获取支持的协议
	GetSupportedProtocols() []string

	// 获取支持的链类型
	GetSupportedChains() []ChainType

	// 从交易中提取业务事件
	ExtractEvents(ctx context.Context, tx *UnifiedTransaction) ([]BusinessEvent, error)

	// 检查是否支持该交易
	SupportsTransaction(tx *UnifiedTransaction) bool
}

// StorageEngine 存储引擎接口
type StorageEngine interface {
	// 事务存储
	StoreTransactions(ctx context.Context, txs []UnifiedTransaction) error

	// 事件存储
	StoreBusinessEvents(ctx context.Context, events []BusinessEvent) error

	// 查询功能
	GetTransactionsByHash(ctx context.Context, hashes []string) ([]UnifiedTransaction, error)
	GetEventsByTxHash(ctx context.Context, txHash string) ([]BusinessEvent, error)
	GetEventsByType(ctx context.Context, eventType BusinessEventType, limit int) ([]BusinessEvent, error)

	// 统计功能
	GetStorageStats(ctx context.Context) (map[string]interface{}, error)

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// ProgressTracker 进度跟踪器接口
type ProgressTracker interface {
	// 基础进度管理
	GetProgress(chainType ChainType) (*ProcessProgress, error)
	UpdateProgress(chainType ChainType, progress *ProcessProgress) error
	ResetProgress(chainType ChainType) error

	// 批量操作
	GetAllProgress() (map[ChainType]*ProcessProgress, error)
	UpdateMultipleProgress(progresses map[ChainType]*ProcessProgress) error

	// 统计信息
	GetProcessingStats(chainType ChainType) (*ProcessingStats, error)
	GetGlobalStats() (*GlobalProcessingStats, error)

	// 状态管理
	SetProcessingStatus(chainType ChainType, status ProcessingStatus) error
	GetProcessingStatus(chainType ChainType) (ProcessingStatus, error)

	// 错误跟踪
	RecordError(chainType ChainType, err error) error
	GetErrorHistory(chainType ChainType, limit int) ([]ProcessingError, error)
	ClearErrorHistory(chainType ChainType) error

	// 性能监控
	RecordProcessingMetrics(chainType ChainType, metrics *ProcessingMetrics) error
	GetPerformanceMetrics(chainType ChainType, duration time.Duration) (*PerformanceReport, error)

	// 健康检查和维护
	HealthCheck() error
	Cleanup(olderThan time.Duration) error
}

// ProcessProgress 处理进度
type ProcessProgress struct {
	ChainType          ChainType        `json:"chain_type"`           // 链类型
	LastProcessedBlock *big.Int         `json:"last_processed_block"` // 最后处理的区块号
	LastUpdateTime     time.Time        `json:"last_update_time"`     // 最后更新时间
	TotalTransactions  int64            `json:"total_transactions"`   // 总交易数
	TotalEvents        int64            `json:"total_events"`         // 总事件数
	ProcessingStatus   ProcessingStatus `json:"processing_status"`    // 处理状态
	StartTime          time.Time        `json:"start_time"`           // 开始处理时间
	LastErrorTime      time.Time        `json:"last_error_time"`      // 最后错误时间
	ErrorCount         int64            `json:"error_count"`          // 错误计数
	SuccessRate        float64          `json:"success_rate"`         // 成功率
}

// ProcessingStatus 处理状态
type ProcessingStatus string

const (
	ProcessingStatusIdle       ProcessingStatus = "idle"        // 空闲
	ProcessingStatusRunning    ProcessingStatus = "running"     // 运行中
	ProcessingStatusPaused     ProcessingStatus = "paused"      // 暂停
	ProcessingStatusError      ProcessingStatus = "error"       // 错误
	ProcessingStatusStopped    ProcessingStatus = "stopped"     // 已停止
	ProcessingStatusSyncing    ProcessingStatus = "syncing"     // 同步中
	ProcessingStatusCatchingUp ProcessingStatus = "catching_up" // 追赶中
)
