package core

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"
	"sync"
	"time"
)

// Engine 统一交易处理引擎
type Engine struct {
	// 链处理器
	chainProcessors map[ChainType]ChainProcessor

	// 事件提取器
	eventExtractors []BusinessEventExtractor

	// 存储引擎
	storage StorageEngine

	// 进度跟踪器
	progressTracker ProgressTracker

	// 配置
	config *EngineConfig

	// 运行状态
	running bool
	mu      sync.RWMutex

	// 取消上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// EngineConfig 引擎配置
type EngineConfig struct {
	// 批处理大小
	BatchSize int `json:"batch_size"`

	// 处理间隔
	ProcessInterval time.Duration `json:"process_interval"`

	// 最大重试次数
	MaxRetries int `json:"max_retries"`

	// 并发处理数
	ConcurrentChains int `json:"concurrent_chains"`

	// 是否启用实时模式
	RealTimeMode bool `json:"real_time_mode"`

	// 链特定配置
	ChainConfigs map[ChainType]*ChainConfig `json:"chain_configs"`
}

// ChainConfig 链配置
type ChainConfig struct {
	Enabled     bool   `json:"enabled"`
	StartBlock  int64  `json:"start_block"`
	BatchSize   int    `json:"batch_size"`
	RpcEndpoint string `json:"rpc_endpoint"`
}

// NewEngine 创建新的处理引擎
func NewEngine(config *EngineConfig) *Engine {
	ctx, cancel := context.WithCancel(context.Background())

	if config == nil {
		config = &EngineConfig{
			BatchSize:        100,
			ProcessInterval:  time.Second * 10,
			MaxRetries:       3,
			ConcurrentChains: 4,
			RealTimeMode:     true,
		}
	}

	return &Engine{
		chainProcessors: make(map[ChainType]ChainProcessor),
		eventExtractors: make([]BusinessEventExtractor, 0),
		config:          config,
		ctx:             ctx,
		cancel:          cancel,
	}
}

// RegisterChainProcessor 注册链处理器
func (e *Engine) RegisterChainProcessor(processor ChainProcessor) {
	e.mu.Lock()
	defer e.mu.Unlock()

	chainType := processor.GetChainType()
	e.chainProcessors[chainType] = processor

	log.Printf("🔗 注册链处理器: %s", chainType)
}

// RegisterEventExtractor 注册事件提取器
func (e *Engine) RegisterEventExtractor(extractor BusinessEventExtractor) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.eventExtractors = append(e.eventExtractors, extractor)

	protocols := extractor.GetSupportedProtocols()
	chains := extractor.GetSupportedChains()
	log.Printf("📦 注册事件提取器: 协议=%v, 链=%v", protocols, chains)
}

// SetStorageEngine 设置存储引擎
func (e *Engine) SetStorageEngine(storage StorageEngine) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.storage = storage
	// 移除冗余日志
}

// SetProgressTracker 设置进度跟踪器
func (e *Engine) SetProgressTracker(tracker ProgressTracker) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.progressTracker = tracker
	log.Printf("📋 设置进度跟踪器")
}

// Start 启动引擎
func (e *Engine) Start() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.running {
		return fmt.Errorf("引擎已经在运行")
	}

	if e.storage == nil {
		return fmt.Errorf("未设置存储引擎")
	}

	if len(e.chainProcessors) == 0 {
		return fmt.Errorf("未注册任何链处理器")
	}

	e.running = true

	// 启动每个链的处理协程
	for chainType, processor := range e.chainProcessors {
		if config, exists := e.config.ChainConfigs[chainType]; exists && !config.Enabled {
			log.Printf("⚠️ 链 %s 已禁用", chainType)
			continue
		}

		go e.processChain(chainType, processor)
		log.Printf("🚀 启动链处理器: %s", chainType)
	}

	log.Printf("🚀 统一交易处理引擎已启动")
	return nil
}

// Stop 停止引擎
func (e *Engine) Stop() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.running {
		return
	}

	e.running = false
	e.cancel()

	log.Printf("🛑 统一交易处理引擎已停止")
}

// IsRunning 检查引擎是否在运行
func (e *Engine) IsRunning() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.running
}

// processChain 处理单个链的数据
func (e *Engine) processChain(chainType ChainType, processor ChainProcessor) {
	ticker := time.NewTicker(e.config.ProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			log.Printf("🛑 链 %s 处理器停止", chainType)
			return
		case <-ticker.C:
			if err := e.processChainBatch(chainType, processor); err != nil {
				log.Printf("❌ 处理链 %s 时出错: %v", chainType, err)
			}
		}
	}
}

// processChainBatch 处理链的一批数据
func (e *Engine) processChainBatch(chainType ChainType, processor ChainProcessor) error {
	// 获取当前进度
	progress, err := e.getOrCreateProgress(chainType)
	if err != nil {
		return fmt.Errorf("获取进度失败: %w", err)
	}

	// 获取最新区块号
	latestBlock, err := processor.GetLatestBlockNumber(e.ctx)
	if err != nil {
		return fmt.Errorf("获取最新区块号失败: %w", err)
	}

	// 计算处理范围
	startBlock := progress.LastProcessedBlock

	if startBlock.Cmp(big.NewInt(0)) == 0 {
		// 如果没有处理过，从配置的起始块开始
		if config, exists := e.config.ChainConfigs[chainType]; exists && config.StartBlock > 0 {
			startBlock = big.NewInt(config.StartBlock)
		} else {
			startBlock = big.NewInt(0).Sub(latestBlock, big.NewInt(100)) // 默认从最新块往前100块开始
		}
	} else {
		// 从下一个区块开始处理（避免重复处理）
		startBlock = big.NewInt(0).Add(startBlock, big.NewInt(1))
	}

	// 确定批处理大小
	batchSize := e.config.BatchSize
	if config, exists := e.config.ChainConfigs[chainType]; exists && config.BatchSize > 0 {
		batchSize = config.BatchSize
	}

	endBlock := big.NewInt(0).Add(startBlock, big.NewInt(int64(batchSize)))
	if endBlock.Cmp(latestBlock) > 0 {
		endBlock = latestBlock
	}

	// 简化的处理范围日志
	log.Printf("📊 [%s] 区块 %s-%s", strings.ToUpper(string(chainType)), startBlock.String(), endBlock.String())

	// 如果没有新块需要处理
	if startBlock.Cmp(endBlock) >= 0 {
		return nil
	}

	log.Printf("🔄 处理链 %s: 块 %s 到 %s", chainType, startBlock.String(), endBlock.String())

	// 获取交易数据 - 添加超时控制
	txCtx, txCancel := context.WithTimeout(e.ctx, time.Minute*5) // 5分钟超时
	defer txCancel()

	log.Printf("⏳ 开始获取交易数据...")
	startTime := time.Now()

	transactions, err := processor.GetTransactionsByBlockRange(txCtx, startBlock, endBlock)
	if err != nil {
		return fmt.Errorf("获取交易数据失败: %w", err)
	}

	duration := time.Since(startTime)
	// 简化日志：只在有交易时输出
	if len(transactions) > 0 {
		log.Printf("📥 [%s] %d笔交易 (%.1fs)", strings.ToUpper(string(chainType)), len(transactions), duration.Seconds())
	}
	// 提取业务事件并过滤DEX相关交易
	allEvents := make([]BusinessEvent, 0)
	dexTransactions := make([]UnifiedTransaction, 0) // 只存储有DEX事件的交易
	dexEventStats := make(map[string]int)            // 统计各DEX的事件数量

	for _, tx := range transactions {
		events, err := e.extractBusinessEvents(&tx)
		if err != nil {
			log.Printf("⚠️ 提取事件失败 (tx: %s): %v", tx.TxHash, err)
			continue
		}

		// 只有包含DEX事件的交易才需要存储
		if len(events) > 0 {
			allEvents = append(allEvents, events...)

			// 统计各DEX的事件数量
			for _, event := range events {
				dexEventStats[event.Protocol]++
			}

			// 将事件添加到交易中
			tx.BusinessEvents = events
			dexTransactions = append(dexTransactions, tx)
		}
	}

	// 显示过滤统计
	if len(transactions) > 0 {
		filterRate := float64(len(dexTransactions)) / float64(len(transactions)) * 100
		log.Printf("🔍 [%s] DEX过滤: %d/%d交易 (%.1f%%)",
			strings.ToUpper(string(chainType)), len(dexTransactions), len(transactions), filterRate)
	}

	// 存储数据 - 只存储DEX相关的交易和事件
	if len(dexTransactions) > 0 {
		if err := e.storage.StoreTransactions(e.ctx, dexTransactions); err != nil {
			return fmt.Errorf("存储交易失败: %w", err)
		}
	}

	if len(allEvents) > 0 {
		if err := e.storage.StoreBusinessEvents(e.ctx, allEvents); err != nil {
			return fmt.Errorf("存储事件失败: %w", err)
		}
	}

	// 优化存储日志 - 显示过滤效果
	if len(dexTransactions) > 0 || len(allEvents) > 0 {
		log.Printf("💾 [%s] 存储 %d/%d交易 %d事件",
			strings.ToUpper(string(chainType)), len(dexTransactions), len(transactions), len(allEvents))
	}

	// 更新进度
	progress.LastProcessedBlock = endBlock
	progress.LastUpdateTime = time.Now()
	progress.TotalTransactions += int64(len(transactions))
	progress.TotalEvents += int64(len(allEvents))

	if e.progressTracker != nil {
		if err := e.progressTracker.UpdateProgress(chainType, progress); err != nil {
			log.Printf("⚠️ %s: 进度保存失败 - %v", chainType, err)
		}
	}

	// 简化完成日志：显示过滤效果和DEX统计
	if len(transactions) > 0 || len(allEvents) > 0 {
		// 构建简洁的DEX统计信息
		dexInfo := ""
		if len(dexEventStats) > 0 {
			dexParts := make([]string, 0, len(dexEventStats))
			for dex, count := range dexEventStats {
				dexParts = append(dexParts, fmt.Sprintf("%s:%d", dex, count))
			}
			dexInfo = fmt.Sprintf(" | %s", strings.Join(dexParts, ","))
		}

		// 显示总交易数/DEX交易数的过滤效果
		log.Printf("✅ [%s] %d/%d交易 %d事件%s (区块:%s)",
			strings.ToUpper(string(chainType)), len(dexTransactions), len(transactions), len(allEvents), dexInfo, endBlock.String())
	}

	return nil
}

// extractBusinessEvents 从交易中提取业务事件
func (e *Engine) extractBusinessEvents(tx *UnifiedTransaction) ([]BusinessEvent, error) {
	allEvents := make([]BusinessEvent, 0)

	for _, extractor := range e.eventExtractors {
		if !extractor.SupportsTransaction(tx) {
			continue
		}

		events, err := extractor.ExtractEvents(e.ctx, tx)
		if err != nil {
			return nil, fmt.Errorf("提取器提取事件失败: %w", err)
		}

		allEvents = append(allEvents, events...)
	}

	return allEvents, nil
}

// getOrCreateProgress 获取或创建处理进度
func (e *Engine) getOrCreateProgress(chainType ChainType) (*ProcessProgress, error) {
	if e.progressTracker == nil {
		log.Printf("⚠️ 链 %s 没有进度跟踪器，使用默认进度", chainType)
		return &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
		}, nil
	}

	progress, err := e.progressTracker.GetProgress(chainType)
	if err != nil {
		log.Printf("⚠️ 链 %s 获取进度失败: %v，创建新进度", chainType, err)
		// 如果没有找到进度记录，创建新的
		progress = &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
		}
	} else {
		log.Printf("✅ 链 %s 成功获取进度: LastProcessedBlock=%s", chainType, progress.LastProcessedBlock.String())
	}

	return progress, nil
}

// GetStats 获取引擎统计信息
func (e *Engine) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	e.mu.RLock()
	stats["running"] = e.running
	stats["registered_chains"] = len(e.chainProcessors)
	stats["registered_extractors"] = len(e.eventExtractors)
	e.mu.RUnlock()

	// 获取存储统计
	if e.storage != nil {
		storageStats, err := e.storage.GetStorageStats(ctx)
		if err == nil {
			stats["storage"] = storageStats
		}
	}

	// 获取链进度
	if e.progressTracker != nil {
		chainProgress := make(map[string]*ProcessProgress)
		for chainType := range e.chainProcessors {
			if progress, err := e.progressTracker.GetProgress(chainType); err == nil {
				chainProgress[string(chainType)] = progress
			}
		}
		stats["chain_progress"] = chainProgress
	}

	return stats, nil
}
