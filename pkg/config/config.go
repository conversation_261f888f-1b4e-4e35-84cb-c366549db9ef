package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	App       AppConfig                 `yaml:"app"`
	Database  DatabaseConfig            `yaml:"database"`
	Redis     RedisConfig               `yaml:"redis"`
	Chains    map[string]ChainConfig    `yaml:"chains"`
	Protocols map[string]ProtocolConfig `yaml:"protocols"`
	Processor ProcessorConfig           `yaml:"processor"`
	Logging   LoggingConfig             `yaml:"logging"`
	Storage   StorageConfig             `yaml:"storage"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name    string `yaml:"name"`
	Version string `yaml:"version"`
	Port    int    `yaml:"port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL MySQLConfig `yaml:"mysql"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	Database        string `yaml:"database"`
	Charset         string `yaml:"charset"`
	ParseTime       bool   `yaml:"parseTime"`
	Loc             string `yaml:"loc"`
	MaxIdleConns    int    `yaml:"maxIdleConns"`
	MaxOpenConns    int    `yaml:"maxOpenConns"`
	ConnMaxLifetime int    `yaml:"connMaxLifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host       string `yaml:"host"`
	Port       int    `yaml:"port"`
	Password   string `yaml:"password"`
	DB         int    `yaml:"db"`
	MaxRetries int    `yaml:"maxRetries"`
	PoolSize   int    `yaml:"poolSize"`
}

// ChainConfig 链配置
type ChainConfig struct {
	Enabled     bool   `yaml:"enabled"`
	RPCEndpoint string `yaml:"rpc_endpoint"`
	ChainID     string `yaml:"chain_id"`
	BatchSize   int    `yaml:"batch_size"`
	Timeout     int    `yaml:"timeout"`
	RetryCount  int    `yaml:"retry_count"`
}

// ProtocolConfig 协议配置
type ProtocolConfig struct {
	Enabled           bool     `yaml:"enabled"`
	Chain             string   `yaml:"chain"`
	ContractAddresses []string `yaml:"contract_addresses"`
}

// ProcessorConfig 处理器配置
type ProcessorConfig struct {
	BatchSize     int `yaml:"batch_size"`
	MaxConcurrent int `yaml:"max_concurrent"`
	RetryDelay    int `yaml:"retry_delay"`
	MaxRetries    int `yaml:"max_retries"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type     string                `yaml:"type"`
	MySQL    MySQLConfig           `yaml:"mysql"`
	InfluxDB InfluxDBStorageConfig `yaml:"influxdb"`
}

// InfluxDBStorageConfig InfluxDB存储配置
type InfluxDBStorageConfig struct {
	URL       string `yaml:"url"`
	Token     string `yaml:"token"`
	Org       string `yaml:"org"`
	Bucket    string `yaml:"bucket"`
	BatchSize int    `yaml:"batch_size"`
	FlushTime int    `yaml:"flush_time"`
	Precision string `yaml:"precision"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 尝试相对路径
		wd, _ := os.Getwd()
		altPath := filepath.Join(wd, configPath)
		if _, err := os.Stat(altPath); os.IsNotExist(err) {
			return nil, fmt.Errorf("配置文件不存在: %s", configPath)
		}
		configPath = altPath
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	return &config, nil
}

// LoadChainConfig 加载指定链的配置文件
// chainType: 链类型 (sui, ethereum, bsc, solana)
// 如果chainType为空，则尝试从环境变量CHAIN_TYPE获取
func LoadChainConfig(chainType string) (*Config, error) {
	// 如果没有指定链类型，尝试从环境变量获取
	if chainType == "" {
		chainType = os.Getenv("CHAIN_TYPE")
	}

	// 如果仍然没有指定，返回错误
	if chainType == "" {
		return nil, fmt.Errorf("未指定链类型，请设置CHAIN_TYPE环境变量或传入chainType参数")
	}

	// 验证链类型
	validChains := []string{"sui", "ethereum", "bsc", "solana"}
	isValid := false
	for _, valid := range validChains {
		if strings.ToLower(chainType) == valid {
			chainType = valid
			isValid = true
			break
		}
	}
	if !isValid {
		return nil, fmt.Errorf("不支持的链类型: %s，支持的类型: %v", chainType, validChains)
	}

	// 直接加载链特定配置文件（现在包含完整配置）
	configPath := fmt.Sprintf("configs/%s.yaml", chainType)

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 尝试相对路径
		wd, _ := os.Getwd()
		altPath := filepath.Join(wd, configPath)
		if _, err := os.Stat(altPath); os.IsNotExist(err) {
			return nil, fmt.Errorf("链配置文件不存在: %s", configPath)
		}
		configPath = altPath
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取链配置文件失败: %w", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析链配置文件失败: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	// App默认值
	if config.App.Name == "" {
		config.App.Name = "unified-tx-parser"
	}
	if config.App.Version == "" {
		config.App.Version = "1.0.0"
	}
	if config.App.Port == 0 {
		config.App.Port = 8081
	}

	// MySQL默认值
	if config.Database.MySQL.Host == "" {
		config.Database.MySQL.Host = "localhost"
	}
	if config.Database.MySQL.Port == 0 {
		config.Database.MySQL.Port = 3306
	}
	if config.Database.MySQL.Username == "" {
		config.Database.MySQL.Username = "root"
	}
	if config.Database.MySQL.Database == "" {
		config.Database.MySQL.Database = "unified_tx_parser"
	}
	if config.Database.MySQL.Charset == "" {
		config.Database.MySQL.Charset = "utf8mb4"
	}
	if config.Database.MySQL.Loc == "" {
		config.Database.MySQL.Loc = "Local"
	}
	if config.Database.MySQL.MaxIdleConns == 0 {
		config.Database.MySQL.MaxIdleConns = 10
	}
	if config.Database.MySQL.MaxOpenConns == 0 {
		config.Database.MySQL.MaxOpenConns = 100
	}
	if config.Database.MySQL.ConnMaxLifetime == 0 {
		config.Database.MySQL.ConnMaxLifetime = 3600
	}

	// Redis默认值
	if config.Redis.Host == "" {
		config.Redis.Host = "localhost"
	}
	if config.Redis.Port == 0 {
		config.Redis.Port = 6379
	}
	if config.Redis.MaxRetries == 0 {
		config.Redis.MaxRetries = 3
	}
	if config.Redis.PoolSize == 0 {
		config.Redis.PoolSize = 10
	}

	// Processor默认值
	if config.Processor.BatchSize == 0 {
		config.Processor.BatchSize = 10
	}
	if config.Processor.MaxConcurrent == 0 {
		config.Processor.MaxConcurrent = 10
	}
	if config.Processor.RetryDelay == 0 {
		config.Processor.RetryDelay = 5
	}
	if config.Processor.MaxRetries == 0 {
		config.Processor.MaxRetries = 3
	}

	// Logging默认值
	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}
	if config.Logging.Format == "" {
		config.Logging.Format = "text"
	}
	if config.Logging.Output == "" {
		config.Logging.Output = "stdout"
	}

	// Storage默认值
	if config.Storage.Type == "" {
		config.Storage.Type = "mysql"
	}
}
