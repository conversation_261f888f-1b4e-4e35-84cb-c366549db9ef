# 统一交易解析器 - 数据流文档

## 📋 概述

本文档详细描述了统一交易解析器的数据处理流程、数据模型设计和存储架构，帮助理解系统如何从区块链网络获取、处理和存储交易数据。

## 🔄 数据处理流程

### 整体处理流程

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  启动引擎   │───▶│  加载配置   │───▶│ 初始化组件  │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 注册协议处理器│◀───│ 注册链处理器 │◀───│             │
└─────────────┘    └─────────────┘    └─────────────┘
        │
        ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 启动处理循环 │───▶│  获取进度   │───▶│ 计算处理范围 │
└─────────────┘    └─────────────┘    └─────────────┘
        ▲                                      │
        │                                      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  更新进度   │◀───│  数据存储   │◀───│ 批量获取交易 │
└─────────────┘    └─────────────┘    └─────────────┘
        ▲                                      │
        │                                      ▼
        │          ┌─────────────┐    ┌─────────────┐
        └──────────│  智能过滤   │◀───│  事件提取   │
                   └─────────────┘    └─────────────┘
```

### 详细处理周期

#### 1. 进度获取阶段
```go
// 从Redis获取上次处理的区块位置
progress, err := progressTracker.GetProgress(chainType)
if err != nil {
    // 首次运行，从配置的起始块开始
    startBlock = config.StartBlock
} else {
    startBlock = progress.LastProcessedBlock + 1
}
```

#### 2. 范围计算阶段
```go
// 获取链上最新区块号
latestBlock, err := processor.GetLatestBlockNumber(ctx)

// 计算本次处理的结束块号
endBlock = min(startBlock + batchSize - 1, latestBlock)

// 确保不超过最新块
if startBlock > latestBlock {
    // 没有新块需要处理
    return nil
}
```

#### 3. 交易获取阶段
```go
// 批量获取区块范围内的所有交易
transactions, err := processor.GetTransactionsByBlockRange(ctx, startBlock, endBlock)

// 日志记录
log.Printf("📥 [%s] %d笔交易 (%.1fs)", chainType, len(transactions), duration.Seconds())
```

#### 4. 事件提取阶段
```go
var filteredTransactions []UnifiedTransaction
var allEvents []BusinessEvent

for _, tx := range transactions {
    // 通过所有协议处理器提取事件
    events, err := dexExtractor.ExtractEvents(ctx, &tx)
    if err != nil {
        continue
    }
    
    // 只保留包含DEX事件的交易
    if len(events) > 0 {
        filteredTransactions = append(filteredTransactions, tx)
        allEvents = append(allEvents, events...)
    }
}
```

#### 5. 数据存储阶段
```go
// 存储过滤后的交易
if len(filteredTransactions) > 0 {
    err := storage.StoreTransactions(ctx, filteredTransactions)
    if err != nil {
        return fmt.Errorf("存储交易失败: %w", err)
    }
}

// 存储业务事件
if len(allEvents) > 0 {
    err := storage.StoreBusinessEvents(ctx, allEvents)
    if err != nil {
        return fmt.Errorf("存储事件失败: %w", err)
    }
}
```

#### 6. 进度更新阶段
```go
// 更新处理进度
progress := &ProcessProgress{
    ChainType:          chainType,
    LastProcessedBlock: endBlock,
    LastUpdateTime:     time.Now(),
    TotalTransactions:  progress.TotalTransactions + int64(len(filteredTransactions)),
    TotalEvents:        progress.TotalEvents + int64(len(allEvents)),
}

err := progressTracker.UpdateProgress(chainType, progress)
```

## 📊 数据模型设计

### 核心数据结构

#### 1. 统一交易模型 (UnifiedTransaction)
```go
type UnifiedTransaction struct {
    // 基础信息
    TxHash      string    `json:"tx_hash"`      // 交易哈希
    ChainType   ChainType `json:"chain_type"`   // 链类型
    BlockNumber *big.Int  `json:"block_number"` // 区块号
    Timestamp   time.Time `json:"timestamp"`    // 时间戳
    
    // 交易详情
    FromAddress string   `json:"from_address"` // 发送方地址
    ToAddress   string   `json:"to_address"`   // 接收方地址
    Value       *big.Int `json:"value"`        // 交易金额
    GasUsed     *big.Int `json:"gas_used"`     // 消耗Gas
    GasPrice    *big.Int `json:"gas_price"`    // Gas价格
    Status      TxStatus `json:"status"`       // 交易状态
    
    // 扩展信息
    Data        []byte                 `json:"data"`         // 交易数据
    Logs        []TransactionLog       `json:"logs"`         // 事件日志
    Metadata    map[string]interface{} `json:"metadata"`     // 元数据
}
```

#### 2. 业务事件模型 (BusinessEvent)
```go
type BusinessEvent struct {
    // 基础信息
    EventID   string              `json:"event_id"`   // 事件ID
    TxHash    string              `json:"tx_hash"`    // 关联交易哈希
    ChainType ChainType           `json:"chain_type"` // 链类型
    Protocol  string              `json:"protocol"`   // 协议名称
    EventType BusinessEventType   `json:"event_type"` // 事件类型
    Timestamp time.Time           `json:"timestamp"`  // 时间戳
    
    // 事件数据
    Data interface{} `json:"data"` // 具体事件数据
}
```

#### 3. 交换事件数据 (SwapEventData)
```go
type SwapEventData struct {
    PoolID    string   `json:"pool_id"`    // 流动性池ID
    TokenIn   string   `json:"token_in"`   // 输入代币
    TokenOut  string   `json:"token_out"`  // 输出代币
    AmountIn  *big.Int `json:"amount_in"`  // 输入数量
    AmountOut *big.Int `json:"amount_out"` // 输出数量
    Price     float64  `json:"price"`      // 交换价格
    Trader    string   `json:"trader"`     // 交易者地址
}
```

#### 4. 流动性事件数据 (LiquidityEventData)
```go
type LiquidityEventData struct {
    PoolID     string   `json:"pool_id"`     // 流动性池ID
    Provider   string   `json:"provider"`    // 流动性提供者
    Token0     string   `json:"token0"`      // 代币0
    Token1     string   `json:"token1"`      // 代币1
    Amount0    *big.Int `json:"amount0"`     // 代币0数量
    Amount1    *big.Int `json:"amount1"`     // 代币1数量
    Liquidity  *big.Int `json:"liquidity"`   // 流动性数量
    ActionType string   `json:"action_type"` // 操作类型 (add/remove)
}
```

### 进度跟踪模型

#### 1. 处理进度 (ProcessProgress)
```go
type ProcessProgress struct {
    ChainType          ChainType        `json:"chain_type"`           // 链类型
    LastProcessedBlock *big.Int         `json:"last_processed_block"` // 最后处理的区块号
    LastUpdateTime     time.Time        `json:"last_update_time"`     // 最后更新时间
    TotalTransactions  int64            `json:"total_transactions"`   // 总交易数
    TotalEvents        int64            `json:"total_events"`         // 总事件数
    ProcessingStatus   ProcessingStatus `json:"processing_status"`    // 处理状态
    StartTime          time.Time        `json:"start_time"`           // 开始处理时间
    ErrorCount         int64            `json:"error_count"`          // 错误计数
    SuccessRate        float64          `json:"success_rate"`         // 成功率
}
```

#### 2. 处理统计 (ProcessingStats)
```go
type ProcessingStats struct {
    ChainType            ChainType `json:"chain_type"`             // 链类型
    TotalBlocks          int64     `json:"total_blocks"`           // 总处理区块数
    TotalTransactions    int64     `json:"total_transactions"`     // 总交易数
    FilteredTransactions int64     `json:"filtered_transactions"`  // 过滤后交易数
    TotalEvents          int64     `json:"total_events"`           // 总事件数
    ProcessingSpeed      float64   `json:"processing_speed"`       // 处理速度 (TPS)
    FilterEfficiency     float64   `json:"filter_efficiency"`      // 过滤效率 (%)
    LastProcessedBlock   *big.Int  `json:"last_processed_block"`   // 最后处理区块
    ProcessingTime       float64   `json:"processing_time"`        // 处理耗时 (秒)
}
```

## 🗄️ 存储设计

### MySQL存储设计

#### 1. 交易表 (transactions)
```sql
CREATE TABLE transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tx_hash VARCHAR(66) NOT NULL UNIQUE,
    chain_type VARCHAR(20) NOT NULL,
    block_number BIGINT NOT NULL,
    timestamp DATETIME NOT NULL,
    from_address VARCHAR(66) NOT NULL,
    to_address VARCHAR(66),
    value DECIMAL(78,0) NOT NULL,
    gas_used BIGINT NOT NULL,
    gas_price BIGINT NOT NULL,
    status ENUM('success', 'failed', 'pending') NOT NULL,
    data LONGTEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_chain_block (chain_type, block_number),
    INDEX idx_timestamp (timestamp),
    INDEX idx_from_address (from_address),
    INDEX idx_to_address (to_address)
);
```

#### 2. 业务事件表 (business_events)
```sql
CREATE TABLE business_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_id VARCHAR(64) NOT NULL UNIQUE,
    tx_hash VARCHAR(66) NOT NULL,
    chain_type VARCHAR(20) NOT NULL,
    protocol VARCHAR(50) NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    timestamp DATETIME NOT NULL,
    event_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_tx_hash (tx_hash),
    INDEX idx_chain_protocol (chain_type, protocol),
    INDEX idx_event_type (event_type),
    INDEX idx_timestamp (timestamp),
    
    FOREIGN KEY (tx_hash) REFERENCES transactions(tx_hash)
);
```

### InfluxDB存储设计

#### 1. 交易数据点 (transactions)
```go
// Measurement: transactions
// Tags: chain_type, tx_hash, from_address, to_address
// Fields: block_number, value, gas_used, gas_price, status
// Time: timestamp

point := influxdb2.NewPoint(
    "transactions",
    map[string]string{
        "chain_type":   string(tx.ChainType),
        "tx_hash":      tx.TxHash,
        "from_address": tx.FromAddress,
        "to_address":   tx.ToAddress,
    },
    map[string]interface{}{
        "block_number": tx.BlockNumber.String(),
        "value":        tx.Value.String(),
        "gas_used":     tx.GasUsed.String(),
        "gas_price":    tx.GasPrice.String(),
        "status":       string(tx.Status),
    },
    tx.Timestamp,
)
```

#### 2. 业务事件数据点 (business_events)
```go
// Measurement: business_events
// Tags: chain_type, tx_hash, event_type, protocol, pool_id, token_in, token_out
// Fields: event_id, amount_in, amount_out, price
// Time: timestamp

point := influxdb2.NewPoint(
    "business_events",
    map[string]string{
        "chain_type": string(event.ChainType),
        "tx_hash":    event.TxHash,
        "event_type": string(event.EventType),
        "protocol":   event.Protocol,
        "pool_id":    swapData.PoolID,
        "token_in":   swapData.TokenIn,
        "token_out":  swapData.TokenOut,
    },
    map[string]interface{}{
        "event_id":   event.EventID,
        "amount_in":  swapData.AmountIn.String(),
        "amount_out": swapData.AmountOut.String(),
        "price":      swapData.Price,
    },
    event.Timestamp,
)
```

### Redis存储设计

#### 1. 进度跟踪键值结构
```
# 进度信息
unified_tx_parser:progress:{chain_type} -> JSON(ProcessProgress)

# 处理状态
unified_tx_parser:status:{chain_type} -> ProcessingStatus

# 错误历史 (列表)
unified_tx_parser:errors:{chain_type} -> [JSON(ProcessingError), ...]

# 处理指标 (列表)
unified_tx_parser:metrics:{chain_type} -> [JSON(ProcessingMetrics), ...]

# 全局统计
unified_tx_parser:global_stats -> JSON(GlobalProcessingStats)
```

## 📈 数据流优化

### 智能过滤机制
```go
// 只存储包含DEX事件的交易，节省98%存储空间
func filterTransactions(transactions []UnifiedTransaction, events []BusinessEvent) []UnifiedTransaction {
    eventTxMap := make(map[string]bool)
    for _, event := range events {
        eventTxMap[event.TxHash] = true
    }
    
    var filtered []UnifiedTransaction
    for _, tx := range transactions {
        if eventTxMap[tx.TxHash] {
            filtered = append(filtered, tx)
        }
    }
    
    return filtered
}
```

### 批量处理优化
```go
// 批量写入，提高性能
func (s *InfluxDBStorage) batchWrite(points []*write.Point) error {
    for _, point := range points {
        s.writeAPI.WritePoint(point)
    }
    
    // 强制刷新
    s.writeAPI.Flush()
    return nil
}
```

### 并发处理优化
```go
// 多链并发处理
func (e *Engine) Start() error {
    for chainType, processor := range e.chainProcessors {
        go e.processChain(chainType, processor) // 每个链独立协程
    }
    return nil
}
```

这个数据流文档详细描述了系统的数据处理机制。接下来我将创建组件设计文档。
