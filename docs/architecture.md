# 统一交易解析器 - 系统架构文档

## 📋 概述

统一交易解析器（Unified Transaction Parser）是一个高性能、多链支持的区块链交易数据处理系统，专门用于从多个区块链网络中提取、解析和存储DeFi协议的交易数据和业务事件。

## 🏗️ 系统架构

### 分层架构设计

系统采用六层分层架构设计，确保高内聚、低耦合：

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层 (Presentation Layer)              │
│  REST API │ CLI工具 │ Web Dashboard │ 监控面板                │
├─────────────────────────────────────────────────────────────┤
│                    核心引擎层 (Core Engine Layer)              │
│  统一处理引擎 │ 配置管理 │ 进度跟踪 │ 错误处理                  │
├─────────────────────────────────────────────────────────────┤
│                  区块链处理层 (Blockchain Layer)               │
│  Ethereum │ BSC │ Solana │ Sui │ 链处理器接口                │
├─────────────────────────────────────────────────────────────┤
│                  协议解析层 (Protocol Layer)                  │
│  Uniswap │ PancakeSwap │ Jupiter │ Bluefin │ 协议处理器接口    │
├─────────────────────────────────────────────────────────────┤
│                    存储层 (Storage Layer)                     │
│  MySQL │ InfluxDB │ Redis │ 内存存储 │ 存储引擎接口            │
├─────────────────────────────────────────────────────────────┤
│                    监控层 (Monitoring Layer)                  │
│  指标收集 │ 健康检查 │ 日志系统 │ 告警系统                    │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件架构

#### 1. 统一处理引擎 (Engine)

**职责**: 系统的核心协调器，管理整个数据处理流程

**核心特性**:
- 多链并发处理支持
- 故障恢复和自动重试
- 进度管理和断点续传
- 实时性能监控

**关键接口**:
```go
type Engine struct {
    chainProcessors map[ChainType]ChainProcessor
    eventExtractors []BusinessEventExtractor
    storage         StorageEngine
    progressTracker ProgressTracker
    config          *EngineConfig
}
```

#### 2. 链处理器 (ChainProcessor)

**职责**: 处理特定区块链的数据获取和转换

**支持的链**:
- **Ethereum**: 以太坊主网处理器
- **BSC**: Binance Smart Chain处理器  
- **Solana**: Solana网络处理器
- **Sui**: Sui网络处理器

**统一接口**:
```go
type ChainProcessor interface {
    GetChainType() ChainType
    GetLatestBlockNumber(ctx context.Context) (*big.Int, error)
    GetTransactionsByBlockRange(ctx context.Context, startBlock, endBlock *big.Int) ([]UnifiedTransaction, error)
    HealthCheck(ctx context.Context) error
}
```

#### 3. 协议解析器 (ProtocolHandler)

**职责**: 从交易中提取特定DeFi协议的业务事件

**支持的协议**:
- **Uniswap**: V2/V3协议支持 (Ethereum/BSC)
- **PancakeSwap**: BSC生态主流DEX
- **Jupiter**: Solana生态聚合器
- **Bluefin**: Sui生态DEX协议

**统一接口**:
```go
type ProtocolHandler interface {
    GetProtocolName() string
    SupportsTransaction(tx *UnifiedTransaction) bool
    ExtractEvents(ctx context.Context, tx *UnifiedTransaction) ([]BusinessEvent, error)
    GetSupportedChains() []ChainType
}
```

#### 4. 存储引擎 (StorageEngine)

**职责**: 提供统一的数据存储抽象

**支持的存储**:
- **MySQL**: 关系型数据库，适合复杂查询
- **InfluxDB**: 时序数据库，适合大规模数据分析
- **Redis**: 内存数据库，用于缓存和进度跟踪
- **Memory**: 内存存储，用于开发测试

**统一接口**:
```go
type StorageEngine interface {
    StoreTransactions(ctx context.Context, transactions []UnifiedTransaction) error
    StoreBusinessEvents(ctx context.Context, events []BusinessEvent) error
    GetTransactionsByHash(ctx context.Context, hashes []string) ([]UnifiedTransaction, error)
    GetEventsByTxHash(ctx context.Context, txHash string) ([]BusinessEvent, error)
}
```

#### 5. 进度跟踪器 (ProgressTracker)

**职责**: 管理处理进度和状态信息

**核心功能**:
- 断点续传支持
- 多链进度独立跟踪
- 处理统计和错误记录
- 性能指标收集

**接口设计**:
```go
type ProgressTracker interface {
    GetProgress(chainType ChainType) (*ProcessProgress, error)
    UpdateProgress(chainType ChainType, progress *ProcessProgress) error
    GetProcessingStats(chainType ChainType) (*ProcessingStats, error)
    RecordError(chainType ChainType, err error) error
}
```

## 🔧 技术栈

### 后端技术
- **语言**: Go 1.21+
- **Web框架**: Gin (REST API)
- **区块链SDK**: 
  - Ethereum: go-ethereum
  - Solana: solana-go
  - Sui: sui-go-sdk
- **配置管理**: YAML + 环境变量
- **并发模型**: Goroutines + Channels

### 数据存储
- **关系型数据库**: MySQL 8.0+
- **时序数据库**: InfluxDB 2.0+
- **缓存/队列**: Redis 6.0+
- **配置存储**: YAML文件

### 基础设施
- **容器化**: Docker + Docker Compose
- **监控**: InfluxDB + Grafana
- **日志**: 结构化日志 + 统一格式
- **健康检查**: HTTP端点 + 自定义检查

### 开发工具
- **依赖管理**: Go Modules
- **构建工具**: Docker Multi-stage Build
- **配置验证**: 自定义验证工具
- **测试**: Go标准测试框架

## 🚀 部署架构

### 单机部署
```
┌─────────────────────────────────────────┐
│              Docker Host                │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Parser    │  │    InfluxDB     │   │
│  │   Service   │  │   (时序数据)     │   │
│  └─────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │    Redis    │  │     Grafana     │   │
│  │  (进度跟踪)  │  │   (监控面板)     │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### 分布式部署
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Parser Node   │  │   Parser Node   │  │   Parser Node   │
│   (Ethereum)    │  │     (BSC)       │  │   (Solana)      │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
┌─────────────────────────────────────────────────────────────┐
│                    共享基础设施                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐     │
│  │    Redis    │  │  InfluxDB   │  │     Grafana     │     │
│  │  (进度跟踪)  │  │  (数据存储) │  │   (监控面板)     │     │
│  └─────────────┘  └─────────────┘  └─────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

## 📊 性能特性

### 处理性能
- **并发处理**: 支持多链同时处理
- **批量操作**: 批量获取和存储，减少网络开销
- **智能过滤**: 只存储包含DEX事件的交易，节省98%存储空间
- **缓存机制**: Redis缓存热点数据和进度信息

### 可扩展性
- **水平扩展**: 支持多实例部署，按链类型分片
- **存储扩展**: 支持多种存储引擎，可根据需求选择
- **协议扩展**: 插件化协议处理器，易于添加新协议
- **链扩展**: 统一链处理器接口，易于支持新区块链

### 可靠性
- **故障恢复**: 自动重试机制和错误处理
- **断点续传**: 基于Redis的进度跟踪，支持服务重启恢复
- **健康检查**: 多层次健康检查，及时发现问题
- **监控告警**: 完整的监控体系和告警机制

## 🔍 接口设计

### REST API
- **健康检查**: `GET /health`
- **处理进度**: `GET /api/v1/progress`
- **系统统计**: `GET /api/v1/stats`
- **交易查询**: `GET /api/v1/transactions`
- **事件查询**: `GET /api/v1/events`

### 配置接口
- **链特定配置**: 支持按链类型加载配置
- **环境变量**: 生产环境支持环境变量覆盖
- **配置验证**: 内置配置验证工具
- **热重载**: 支持配置文件热重载（部分配置）

## 🛡️ 安全设计

### 数据安全
- **连接加密**: 支持TLS/SSL连接
- **访问控制**: 基于Token的API访问控制
- **数据脱敏**: 敏感数据脱敏处理
- **审计日志**: 完整的操作审计日志

### 系统安全
- **容器隔离**: Docker容器化部署
- **网络隔离**: 内部服务网络隔离
- **资源限制**: 容器资源限制和监控
- **安全扫描**: 定期安全漏洞扫描

这个架构文档提供了系统的整体设计概览。接下来我将创建详细的数据流文档。
