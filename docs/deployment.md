# 统一交易解析器 - 部署和运维文档

## 📋 概述

本文档详细描述统一交易解析器的部署架构、监控体系、运维流程和最佳实践，为生产环境部署提供完整指南。

## 🏗️ 部署架构

### 单机部署架构

适用于开发、测试和小规模生产环境：

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Host                              │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Parser Service │  │    InfluxDB     │                  │
│  │   (主服务)       │  │   (时序数据库)   │                  │
│  │  Port: 8081     │  │   Port: 8086    │                  │
│  └─────────────────┘  └─────────────────┘                  │
│           │                     │                          │
│           └─────────┬───────────┘                          │
│                     │                                      │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │     Redis       │  │     Grafana     │                  │
│  │  (进度跟踪/缓存) │  │   (监控面板)     │                  │
│  │   Port: 6379    │  │   Port: 3000    │                  │
│  └─────────────────┘  └─────────────────┘                  │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │                 Docker Network                          │
│  │              unified-tx-parser                          │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

### 分布式部署架构

适用于大规模生产环境：

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Parser Node   │  │   Parser Node   │  │   Parser Node   │
│   (Ethereum)    │  │     (BSC)       │  │   (Solana)      │
│                 │  │                 │  │                 │
│  ┌─────────────┐│  │┌─────────────┐  │  │┌─────────────┐  │
│  │   Parser    ││  ││   Parser    │  │  ││   Parser    │  │
│  │   Service   ││  ││   Service   │  │  ││   Service   │  │
│  └─────────────┘│  │└─────────────┘  │  │└─────────────┘  │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
┌─────────────────────────────────────────────────────────────┐
│                    共享基础设施层                            │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐     │
│  │    Redis    │  │  InfluxDB   │  │     Grafana     │     │
│  │   Cluster   │  │   Cluster   │  │    Dashboard    │     │
│  │ (进度跟踪)   │  │  (数据存储) │  │   (监控面板)     │     │
│  └─────────────┘  └─────────────┘  └─────────────────┘     │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐     │
│  │   Nginx     │  │ Prometheus  │  │   AlertManager  │     │
│  │ (负载均衡)   │  │ (指标收集)   │  │   (告警管理)     │     │
│  └─────────────┘  └─────────────┘  └─────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

### 云原生部署架构

适用于Kubernetes环境：

```
┌─────────────────────────────────────────────────────────────┐
│                    Kubernetes Cluster                      │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │                   Namespace: parser                     │
│  │                                                         │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │   Parser    │  │   Parser    │  │   Parser    │     │
│  │  │ Deployment  │  │ Deployment  │  │ Deployment  │     │
│  │  │ (Ethereum)  │  │   (BSC)     │  │  (Solana)   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  │                                                         │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │   Service   │  │   Service   │  │   Service   │     │
│  │  │ (Ethereum)  │  │   (BSC)     │  │  (Solana)   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │                Namespace: infrastructure                │
│  │                                                         │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │    Redis    │  │  InfluxDB   │  │   Grafana   │     │
│  │  │ StatefulSet │  │ StatefulSet │  │ Deployment  │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  │                                                         │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │ Prometheus  │  │AlertManager │  │   Ingress   │     │
│  │  │ Deployment  │  │ Deployment  │  │ Controller  │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

## 🚀 部署方式

### Docker Compose部署

#### 1. 简单部署（推荐用于开发/测试）
```bash
# 克隆项目
git clone https://github.com/your-org/unified-tx-parser.git
cd unified-tx-parser

# 启动服务栈
docker-compose -f docker/docker-compose-simple.yml up -d

# 查看服务状态
docker-compose -f docker/docker-compose-simple.yml ps

# 查看日志
docker-compose -f docker/docker-compose-simple.yml logs -f unified-tx-parser
```

#### 2. 完整部署（包含监控）
```bash
# 启动完整服务栈
docker-compose -f docker/docker-compose.yml up -d

# 服务访问地址
# API服务: http://localhost:8081
# InfluxDB: http://localhost:8086
# Grafana: http://localhost:3000 (admin/admin)
# Redis: localhost:6379
```

#### 3. 链特定部署
```bash
# 只部署Ethereum处理器
CHAIN_TYPE=ethereum docker-compose -f docker/docker-compose-chains.yml up -d

# 只部署BSC处理器
CHAIN_TYPE=bsc docker-compose -f docker/docker-compose-chains.yml up -d

# 只部署Solana处理器
CHAIN_TYPE=solana docker-compose -f docker/docker-compose-chains.yml up -d
```

### Kubernetes部署

#### 1. 准备配置文件
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: unified-tx-parser
---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: parser-config
  namespace: unified-tx-parser
data:
  config.yaml: |
    app:
      name: "unified-tx-parser"
      version: "2.0.0"
      port: 8081
    # ... 其他配置
```

#### 2. 部署Redis
```yaml
# k8s/redis.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
  namespace: unified-tx-parser
spec:
  serviceName: redis
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-data
          mountPath: /data
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

#### 3. 部署Parser服务
```yaml
# k8s/parser-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parser-ethereum
  namespace: unified-tx-parser
spec:
  replicas: 2
  selector:
    matchLabels:
      app: parser
      chain: ethereum
  template:
    metadata:
      labels:
        app: parser
        chain: ethereum
    spec:
      containers:
      - name: parser
        image: unified-tx-parser:latest
        env:
        - name: CHAIN_TYPE
          value: "ethereum"
        ports:
        - containerPort: 8081
        volumeMounts:
        - name: config
          mountPath: /app/configs
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: parser-config
```

### 手动部署

#### 1. 环境准备
```bash
# 安装Go 1.21+
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
export PATH=$PATH:/usr/local/go/bin

# 安装依赖服务
# Redis
sudo apt-get install redis-server

# InfluxDB
wget https://dl.influxdata.com/influxdb/releases/influxdb2-2.7.0-amd64.deb
sudo dpkg -i influxdb2-2.7.0-amd64.deb
```

#### 2. 编译部署
```bash
# 克隆代码
git clone https://github.com/your-org/unified-tx-parser.git
cd unified-tx-parser

# 编译
go mod tidy
go build -o bin/parser cmd/parser/main.go

# 配置文件
cp configs/config.yaml.example configs/config.yaml
vim configs/config.yaml

# 启动服务
./bin/parser
```

## 📊 监控体系

### 指标收集

#### 1. 应用指标
```go
// 内置指标收集
type ProcessingMetrics struct {
    ChainType         ChainType `json:"chain_type"`
    ProcessingSpeed   float64   `json:"processing_speed"`   // TPS
    FilterEfficiency  float64   `json:"filter_efficiency"`  // 过滤效率
    ErrorRate         float64   `json:"error_rate"`         // 错误率
    MemoryUsage       int64     `json:"memory_usage"`       // 内存使用
    CPUUsage          float64   `json:"cpu_usage"`          // CPU使用率
    NetworkLatency    float64   `json:"network_latency"`    // 网络延迟
    StorageLatency    float64   `json:"storage_latency"`    // 存储延迟
}
```

#### 2. 系统指标
- **CPU使用率**: 各个处理器的CPU占用
- **内存使用**: 堆内存、栈内存使用情况
- **网络I/O**: 与区块链节点的网络通信
- **磁盘I/O**: 数据库读写性能
- **Goroutine数量**: 并发协程监控

#### 3. 业务指标
- **处理速度**: 每秒处理交易数(TPS)
- **过滤效率**: DEX交易过滤比例
- **数据质量**: 数据完整性和准确性
- **延迟指标**: 端到端处理延迟

### Grafana仪表板

#### 1. 系统概览仪表板
```json
{
  "dashboard": {
    "title": "统一交易解析器 - 系统概览",
    "panels": [
      {
        "title": "处理速度 (TPS)",
        "type": "stat",
        "targets": [
          {
            "query": "SELECT mean(processing_speed) FROM processing_metrics WHERE time >= now() - 1h GROUP BY time(5m), chain_type"
          }
        ]
      },
      {
        "title": "过滤效率",
        "type": "gauge",
        "targets": [
          {
            "query": "SELECT mean(filter_efficiency) FROM processing_metrics WHERE time >= now() - 1h GROUP BY chain_type"
          }
        ]
      }
    ]
  }
}
```

#### 2. 链特定仪表板
- **Ethereum仪表板**: 以太坊处理器专用监控
- **BSC仪表板**: BSC处理器专用监控
- **Solana仪表板**: Solana处理器专用监控
- **Sui仪表板**: Sui处理器专用监控

### 告警规则

#### 1. Prometheus告警规则
```yaml
# alerts.yml
groups:
- name: unified-tx-parser
  rules:
  - alert: HighErrorRate
    expr: rate(processing_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "处理错误率过高"
      description: "{{ $labels.chain_type }} 链的错误率超过10%"

  - alert: ProcessingLag
    expr: (latest_block_number - last_processed_block) > 100
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "处理延迟过大"
      description: "{{ $labels.chain_type }} 链处理延迟超过100个区块"

  - alert: ServiceDown
    expr: up{job="unified-tx-parser"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "服务不可用"
      description: "统一交易解析器服务已停止"
```

#### 2. 告警通知
```yaml
# alertmanager.yml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://webhook-server:5001/alerts'
    send_resolved: true

- name: 'slack'
  slack_configs:
  - api_url: 'YOUR_SLACK_WEBHOOK_URL'
    channel: '#alerts'
    title: '统一交易解析器告警'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
```

## 🔧 运维操作

### 日常运维

#### 1. 健康检查
```bash
# API健康检查
curl http://localhost:8081/health

# 获取处理进度
curl http://localhost:8081/api/v1/progress

# 获取系统统计
curl http://localhost:8081/api/v1/stats
```

#### 2. 日志管理
```bash
# 查看实时日志
docker-compose logs -f unified-tx-parser

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59" unified-tx-parser

# 日志轮转配置
# /etc/logrotate.d/unified-tx-parser
/var/log/unified-tx-parser/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 parser parser
}
```

#### 3. 数据备份
```bash
# InfluxDB备份
influx backup /backup/influxdb-$(date +%Y%m%d)

# Redis备份
redis-cli --rdb /backup/redis-$(date +%Y%m%d).rdb

# 配置文件备份
tar -czf /backup/configs-$(date +%Y%m%d).tar.gz configs/
```

### 故障处理

#### 1. 常见问题诊断
```bash
# 检查服务状态
systemctl status unified-tx-parser

# 检查端口占用
netstat -tlnp | grep 8081

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查进程状态
ps aux | grep parser
```

#### 2. 性能调优
```yaml
# 配置优化
processor:
  batch_size: 50        # 根据网络情况调整
  max_concurrent: 5     # 根据CPU核心数调整
  retry_delay: 5        # 网络重试延迟
  max_retries: 3        # 最大重试次数

storage:
  influxdb:
    batch_size: 2000    # 批量写入大小
    flush_time: 5       # 刷新间隔
```

#### 3. 扩容操作
```bash
# 水平扩容 - 添加新的处理节点
docker-compose -f docker/docker-compose-scale.yml up -d --scale parser=3

# 垂直扩容 - 增加资源限制
docker-compose -f docker/docker-compose.yml up -d --force-recreate
```

这个部署和运维文档提供了完整的生产环境部署指南。现在让我创建一个综合的架构图来总结整个系统。
