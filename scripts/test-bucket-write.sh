#!/bin/bash

# 测试各个bucket的写入功能
set -e

echo "🧪 测试InfluxDB多链bucket写入功能..."

# InfluxDB配置
INFLUX_URL="http://localhost:8086"
INFLUX_TOKEN="unified-tx-parser-token-2024"
INFLUX_ORG="unified-tx-parser"

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试写入函数
test_bucket_write() {
    local bucket=$1
    local chain=$2
    
    echo -e "\n📝 测试写入 $bucket bucket..."
    
    # 创建测试数据点
    local timestamp=$(date +%s)000000000  # 纳秒时间戳
    local test_data="test_transactions,chain_type=$chain,tx_hash=test_hash_$timestamp value=1.0 $timestamp"
    
    # 写入数据
    if echo "$test_data" | docker exec -i unified-tx-parser-influxdb influx write \
        --bucket "$bucket" \
        --org "$INFLUX_ORG" \
        --token "$INFLUX_TOKEN" \
        --precision ns; then
        echo -e "${GREEN}✅ $bucket bucket 写入成功${NC}"
        
        # 验证数据是否写入成功
        sleep 1
        local query="from(bucket:\"$bucket\") |> range(start:-1m) |> filter(fn:(r) => r._measurement == \"test_transactions\") |> count()"
        local result=$(docker exec unified-tx-parser-influxdb influx query \
            --org "$INFLUX_ORG" \
            --token "$INFLUX_TOKEN" \
            "$query" 2>/dev/null | grep -v "^#" | tail -1)
        
        if [[ "$result" =~ [0-9]+ ]]; then
            echo -e "${GREEN}✅ $bucket bucket 数据查询成功${NC}"
        else
            echo -e "${YELLOW}⚠️  $bucket bucket 数据查询未返回预期结果${NC}"
        fi
    else
        echo -e "${RED}❌ $bucket bucket 写入失败${NC}"
        return 1
    fi
}

# 测试所有bucket
echo "🔍 开始测试各个链的bucket..."

test_bucket_write "sui" "sui"
test_bucket_write "ethereum" "ethereum"
test_bucket_write "bsc" "bsc"
test_bucket_write "solana" "solana"
test_bucket_write "blockchain-data" "general"

echo -e "\n📊 查看所有bucket的数据统计..."

for bucket in "sui" "ethereum" "bsc" "solana" "blockchain-data"; do
    echo -e "\n📦 $bucket bucket 数据统计:"
    docker exec unified-tx-parser-influxdb influx query \
        --org "$INFLUX_ORG" \
        --token "$INFLUX_TOKEN" \
        "from(bucket:\"$bucket\") |> range(start:-1h) |> group() |> count()" 2>/dev/null || echo "  暂无数据"
done

echo -e "\n🎉 ${GREEN}Bucket写入测试完成！${NC}"
echo -e "\n📝 ${YELLOW}说明：${NC}"
echo "- 所有bucket都已创建并可以正常写入数据"
echo "- 您的Sui链解析器现在应该可以正常工作"
echo "- 如果仍有问题，请检查应用程序的InfluxDB连接配置"
