#!/bin/bash

# 测试多链bucket配置脚本
# 验证各个链配置文件中的bucket配置是否正确

set -e

echo "🧪 开始测试多链bucket配置..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_config() {
    local chain=$1
    local expected_bucket=$2
    local config_file="configs/${chain}.yaml"
    
    echo -e "\n📋 测试 ${chain} 链配置..."
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}❌ 配置文件不存在: $config_file${NC}"
        return 1
    fi
    
    # 提取bucket配置
    local bucket=$(grep -A 10 "influxdb:" "$config_file" | grep "bucket:" | sed 's/.*bucket: *"\([^"]*\)".*/\1/')
    
    if [ "$bucket" = "$expected_bucket" ]; then
        echo -e "${GREEN}✅ Bucket配置正确: $bucket${NC}"
        return 0
    else
        echo -e "${RED}❌ Bucket配置错误: 期望 '$expected_bucket', 实际 '$bucket'${NC}"
        return 1
    fi
}

# 测试各个链的配置
echo "🔍 检查各链配置文件中的bucket设置..."

test_config "sui" "sui"
test_config "ethereum" "ethereum"
test_config "bsc" "bsc"
test_config "solana" "solana"

# 测试主配置文件
echo -e "\n📋 测试主配置文件..."
config_file="configs/config.yaml"
if [ ! -f "$config_file" ]; then
    echo -e "${RED}❌ 主配置文件不存在: $config_file${NC}"
    exit 1
fi

bucket=$(grep -A 10 "influxdb:" "$config_file" | grep "bucket:" | sed 's/.*bucket: *"\([^"]*\)".*/\1/')
if [ "$bucket" = "blockchain-data" ]; then
    echo -e "${GREEN}✅ 主配置文件bucket配置正确: $bucket${NC}"
else
    echo -e "${RED}❌ 主配置文件bucket配置错误: 期望 'blockchain-data', 实际 '$bucket'${NC}"
    exit 1
fi

# 检查Docker配置
echo -e "\n🐳 检查Docker配置..."

# 检查初始化脚本
if [ -f "docker/influxdb/init-buckets.sh" ]; then
    echo -e "${GREEN}✅ InfluxDB初始化脚本存在${NC}"
    if [ -x "docker/influxdb/init-buckets.sh" ]; then
        echo -e "${GREEN}✅ 初始化脚本有执行权限${NC}"
    else
        echo -e "${YELLOW}⚠️  初始化脚本没有执行权限，正在设置...${NC}"
        chmod +x docker/influxdb/init-buckets.sh
        echo -e "${GREEN}✅ 执行权限已设置${NC}"
    fi
else
    echo -e "${RED}❌ InfluxDB初始化脚本不存在${NC}"
    exit 1
fi

# 检查Docker Compose文件
echo -e "\n📦 检查Docker Compose配置..."

for compose_file in "docker/docker-compose-simple.yml" "docker/docker-compose-chains.yml"; do
    if [ -f "$compose_file" ]; then
        if grep -q "init-buckets.sh" "$compose_file"; then
            echo -e "${GREEN}✅ $compose_file 包含初始化脚本配置${NC}"
        else
            echo -e "${RED}❌ $compose_file 缺少初始化脚本配置${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  $compose_file 不存在${NC}"
    fi
done

# 检查Grafana数据源配置
echo -e "\n📊 检查Grafana数据源配置..."
grafana_config="docker/grafana/provisioning/datasources/influxdb.yml"

if [ -f "$grafana_config" ]; then
    echo -e "${GREEN}✅ Grafana数据源配置文件存在${NC}"
    
    # 检查各个链的数据源
    chains=("Sui" "Ethereum" "BSC" "Solana")
    for chain in "${chains[@]}"; do
        if grep -q "InfluxDB-$chain" "$grafana_config"; then
            echo -e "${GREEN}✅ $chain 链数据源配置存在${NC}"
        else
            echo -e "${RED}❌ $chain 链数据源配置缺失${NC}"
        fi
    done
else
    echo -e "${RED}❌ Grafana数据源配置文件不存在${NC}"
fi

echo -e "\n🎉 ${GREEN}多链bucket配置测试完成！${NC}"
echo -e "\n📝 ${YELLOW}使用说明：${NC}"
echo "1. 启动特定链服务: ./unified-tx-parser -chain sui"
echo "2. 使用环境变量: CHAIN_TYPE=ethereum ./unified-tx-parser"
echo "3. 启动所有链服务: docker-compose -f docker/docker-compose-chains.yml up -d"
echo "4. 在Grafana中选择对应的数据源查询特定链的数据"
