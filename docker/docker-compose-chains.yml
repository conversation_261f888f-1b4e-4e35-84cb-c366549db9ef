version: '3.8'

services:
  # MySQL数据库 - 共享存储
  mysql:
    image: mysql:8.0
    container_name: unified_tx_parser_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: unified_tx_parser
      MYSQL_USER: parser_user
      MYSQL_PASSWORD: parser_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - unified_tx_parser

  # Redis - 共享进度跟踪
  redis:
    image: redis:7-alpine
    container_name: unified_tx_parser_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - unified_tx_parser

  # InfluxDB - 时序数据存储
  influxdb:
    image: influxdb:2.7
    container_name: unified_tx_parser_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: unified-tx-parser
      DOCKER_INFLUXDB_INIT_BUCKET: blockchain-data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: unified-tx-parser-token-2024
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - ./influxdb/init-buckets.sh:/docker-entrypoint-initdb.d/init-buckets.sh:ro
    networks:
      - unified_tx_parser

  # Sui链解析器
  sui-parser:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: sui_tx_parser
    environment:
      CHAIN_TYPE: "sui"
    ports:
      - "8081:8081"
    depends_on:
      - redis
      - influxdb
    networks:
      - unified_tx_parser
    restart: unless-stopped
    profiles:
      - sui
      - all

  # Ethereum链解析器
  ethereum-parser:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: ethereum_tx_parser
    environment:
      CHAIN_TYPE: "ethereum"
    ports:
      - "8082:8081"
    depends_on:
      - redis
      - influxdb
    networks:
      - unified_tx_parser
    restart: unless-stopped
    profiles:
      - ethereum
      - all

  # BSC链解析器
  bsc-parser:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: bsc_tx_parser
    environment:
      CHAIN_TYPE: "bsc"
    ports:
      - "8083:8081"
    depends_on:
      - redis
      - influxdb
    networks:
      - unified_tx_parser
    restart: unless-stopped
    profiles:
      - bsc
      - all

  # Solana链解析器
  solana-parser:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: solana_tx_parser
    environment:
      CHAIN_TYPE: "solana"
    ports:
      - "8084:8081"
    depends_on:
      - redis
      - influxdb
    networks:
      - unified_tx_parser
    restart: unless-stopped
    profiles:
      - solana
      - all

  # phpMyAdmin (数据库管理界面)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: unified_tx_parser_phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8090:80"
    depends_on:
      - mysql
    networks:
      - unified_tx_parser
    profiles:
      - tools
      - all

  # Grafana (监控界面)
  grafana:
    image: grafana/grafana:latest
    container_name: unified_tx_parser_grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - influxdb
    networks:
      - unified_tx_parser
    profiles:
      - tools
      - all

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
  grafana_data:

networks:
  unified_tx_parser:
    driver: bridge
