-- 创建数据库
CREATE DATABASE IF NOT EXISTS unified_tx_parser CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE unified_tx_parser;

-- 统一交易表
CREATE TABLE IF NOT EXISTS unified_transactions (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    block_number BIGINT NOT NULL,
    tx_hash VARCHAR(255) NOT NULL UNIQUE,
    from_address VARCHAR(255),
    to_address VARCHAR(255),
    value VARCHAR(255),
    gas_used BIGINT,
    gas_price VARCHAR(255),
    status VARCHAR(20) NOT NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_chain_block (chain_id, block_number),
    INDEX idx_timestamp (timestamp),
    INDEX idx_from_address (from_address),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 统一事件表
CREATE TABLE IF NOT EXISTS unified_events (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    tx_hash VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    protocol VARCHAR(50) NOT NULL,
    contract_address VARCHAR(255),
    block_number BIGINT NOT NULL,
    log_index INT,
    event_data JSON,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_chain_protocol (chain_id, protocol),
    INDEX idx_event_type (event_type),
    INDEX idx_block_number (block_number),
    INDEX idx_timestamp (timestamp),
    FOREIGN KEY (tx_hash) REFERENCES unified_transactions(tx_hash) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 统一池表
CREATE TABLE IF NOT EXISTS unified_pools (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    protocol VARCHAR(50) NOT NULL,
    pool_address VARCHAR(255) NOT NULL,
    token0_address VARCHAR(255),
    token1_address VARCHAR(255),
    token0_symbol VARCHAR(20),
    token1_symbol VARCHAR(20),
    fee_tier VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_pool (chain_id, protocol, pool_address),
    INDEX idx_protocol (protocol),
    INDEX idx_tokens (token0_address, token1_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 统一代币表
CREATE TABLE IF NOT EXISTS unified_tokens (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    token_address VARCHAR(255) NOT NULL,
    symbol VARCHAR(20),
    name VARCHAR(100),
    decimals INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_token (chain_id, token_address),
    INDEX idx_symbol (symbol)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 统一流动性操作表
CREATE TABLE IF NOT EXISTS unified_liquidity (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    tx_hash VARCHAR(255) NOT NULL,
    pool_id VARCHAR(255),
    operation_type VARCHAR(50) NOT NULL,
    user_address VARCHAR(255),
    token0_amount VARCHAR(255),
    token1_amount VARCHAR(255),
    liquidity_amount VARCHAR(255),
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_pool_operation (pool_id, operation_type),
    INDEX idx_user (user_address),
    INDEX idx_timestamp (timestamp),
    FOREIGN KEY (tx_hash) REFERENCES unified_transactions(tx_hash) ON DELETE CASCADE,
    FOREIGN KEY (pool_id) REFERENCES unified_pools(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 统一储备金表
CREATE TABLE IF NOT EXISTS unified_reserves (
    id VARCHAR(255) PRIMARY KEY,
    chain_id VARCHAR(50) NOT NULL,
    pool_id VARCHAR(255) NOT NULL,
    token0_reserve VARCHAR(255),
    token1_reserve VARCHAR(255),
    block_number BIGINT NOT NULL,
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_pool_block (pool_id, block_number),
    INDEX idx_timestamp (timestamp),
    FOREIGN KEY (pool_id) REFERENCES unified_pools(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些测试数据
INSERT IGNORE INTO unified_tokens (id, chain_id, token_address, symbol, name, decimals) VALUES
('sui-mainnet-0x2::sui::SUI', 'sui-mainnet', '0x2::sui::SUI', 'SUI', 'Sui', 9),
('sui-mainnet-0x2::coin::Coin', 'sui-mainnet', '0x2::coin::Coin', 'COIN', 'Generic Coin', 6);

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'parser_user'@'%' IDENTIFIED BY 'parser_pass';
GRANT ALL PRIVILEGES ON unified_tx_parser.* TO 'parser_user'@'%';
FLUSH PRIVILEGES;
