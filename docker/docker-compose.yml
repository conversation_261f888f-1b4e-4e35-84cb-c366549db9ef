version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: unified_tx_parser_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: unified_tx_parser
      MYSQL_USER: parser_user
      MYSQL_PASSWORD: parser_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - unified_tx_parser

  # Redis (可选，用于缓存和进度跟踪)
  redis:
    image: redis:7-alpine
    container_name: unified_tx_parser_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - unified_tx_parser

  # phpMyAdmin (数据库管理界面)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: unified_tx_parser_phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - unified_tx_parser

  # 统一交易解析器
  unified-tx-parser:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: unified_tx_parser_app
    environment:
      # 数据库配置
      MYSQL_HOST: mysql
      MYSQL_USER: parser_user
      MYSQL_PASSWORD: parser_pass
      MYSQL_DATABASE: unified_tx_parser
      
      # 链配置
      SUI_ENABLED: "true"
      SUI_RPC_ENDPOINT: "https://fullnode.mainnet.sui.io:443"
      SUI_CHAIN_ID: "sui-mainnet"
      
      ETH_ENABLED: "false"
      ETH_RPC_ENDPOINT: "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
      
      BSC_ENABLED: "false"
      BSC_RPC_ENDPOINT: "https://bsc-dataseed.binance.org/"
      
      SOLANA_ENABLED: "false"
      SOLANA_RPC_ENDPOINT: "https://api.mainnet-beta.solana.com"
      
      # API配置
      PORT: "8080"
      
      # 其他配置
      GIN_MODE: "release"
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - unified_tx_parser
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  unified_tx_parser:
    driver: bridge 