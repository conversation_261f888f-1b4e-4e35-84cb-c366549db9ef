version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: unified-tx-parser-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password123
      MYSQL_DATABASE: unified_tx_parser
      MYSQL_USER: parser_user
      MYSQL_PASSWORD: parser_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:7-alpine
    container_name: unified-tx-parser-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5
      interval: 10s
      start_period: 30s

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: unified-tx-parser-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8082:8081"
    depends_on:
      redis:
        condition: service_healthy

  influxdb:
    image: influxdb:2.7
    container_name: unified-tx-parser-influxdb
    restart: unless-stopped
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: admin123456
      DOCKER_INFLUXDB_INIT_ORG: unified-tx-parser
      DOCKER_INFLUXDB_INIT_BUCKET: blockchain-data
      DOCKER_INFLUXDB_INIT_RETENTION: 90d
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: unified-tx-parser-token-2024
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
      - ./influxdb/init-buckets.sh:/docker-entrypoint-initdb.d/init-buckets.sh:ro
    healthcheck:
      test: ["CMD", "influx", "ping"]
      timeout: 10s
      retries: 5
      interval: 10s
      start_period: 30s

  grafana:
    image: grafana/grafana:latest
    container_name: unified-tx-parser-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-influxdb-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      influxdb:
        condition: service_healthy

  phpmyadmin:
    image: phpmyadmin:latest
    container_name: unified-tx-parser-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password123
    ports:
      - "8080:80"
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
  influxdb_config:
  grafana_data: